'use client';

import { useState, useEffect } from 'react';
import { X, Save, Mail, Phone, User, School, Hash, MessageSquare } from 'lucide-react';
import { useDashboard } from '@/contexts/DashboardContext';
import { motion } from 'framer-motion';

export default function ParticipantModal({ participant, isOpen, onClose, mode = 'view' }) {
  const { updateParticipant } = useDashboard();
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    discord_tag: '',
    university: '',
    student_id: '',
    skills: '',
    expectations: '',
    has_team: false,
    team_name: '',
    team_member_1: '',
    team_member_2: '',
    team_member_3: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (participant) {
      setFormData({
        first_name: participant.first_name || '',
        last_name: participant.last_name || '',
        email: participant.email || '',
        phone: participant.phone || '',
        discord_tag: participant.discord_tag || '',
        university: participant.university || '',
        student_id: participant.student_id || '',
        skills: participant.skills || '',
        expectations: participant.expectations || '',
        has_team: participant.has_team || false,
        team_name: participant.team_name || '',
        team_member_1: participant.team_member_1 || '',
        team_member_2: participant.team_member_2 || '',
        team_member_3: participant.team_member_3 || ''
      });
    }
  }, [participant]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSave = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await updateParticipant(participant.id, formData);
      onClose();
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !participant) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-[70] backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl max-w-4xl w-full max-h-[90vh]"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10 bg-gradient-to-r from-cyan-900/10 to-fuchsia-900/10">
          <div>
            <h2 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-300 to-fuchsia-300">
              {mode === 'edit' ? 'Edit Participant' : 'Participant Details'}
            </h2>
            <p className="text-sm text-cyan-100/80">
              {participant.first_name} {participant.last_name}
            </p>
          </div>
          <motion.button
            onClick={onClose}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-white/10 rounded-lg transition-colors text-white/60 hover:text-white"
          >
            <X className="w-5 h-5" />
          </motion.button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4 p-4 rounded-xl bg-red-600/20 border border-red-400/50 text-white"
            >
              {error}
            </motion.div>
          )}

          <form onSubmit={handleSave} className="space-y-6">
            {/* Personal Information */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
                <User className="w-5 h-5 text-cyan-400" />
                Personal Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    First Name
                  </label>
                  {mode === 'edit' ? (
                    <input
                      type="text"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleInputChange}
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                      required
                    />
                  ) : (
                    <p className="text-white/80">{participant.first_name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Last Name
                  </label>
                  {mode === 'edit' ? (
                    <input
                      type="text"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleInputChange}
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                      required
                    />
                  ) : (
                    <p className="text-white/80">{participant.last_name}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
                <Mail className="w-5 h-5 text-cyan-400" />
                Contact Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Email
                  </label>
                  {mode === 'edit' ? (
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                      required
                    />
                  ) : (
                    <p className="text-white/80">{participant.email}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Phone
                  </label>
                  {mode === 'edit' ? (
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                      required
                    />
                  ) : (
                    <p className="text-white/80">{participant.phone}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Discord Tag
                  </label>
                  {mode === 'edit' ? (
                    <input
                      type="text"
                      name="discord_tag"
                      value={formData.discord_tag}
                      onChange={handleInputChange}
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                    />
                  ) : (
                    <p className="text-white/80">{participant.discord_tag || 'Not provided'}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Academic Information */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
                <School className="w-5 h-5 text-cyan-400" />
                Academic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    University
                  </label>
                  {mode === 'edit' ? (
                    <input
                      type="text"
                      name="university"
                      value={formData.university}
                      onChange={handleInputChange}
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                      required
                    />
                  ) : (
                    <p className="text-white/80">{participant.university}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Student ID
                  </label>
                  {mode === 'edit' ? (
                    <input
                      type="text"
                      name="student_id"
                      value={formData.student_id}
                      onChange={handleInputChange}
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                      required
                    />
                  ) : (
                    <p className="text-white/80">{participant.student_id}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Skills and Expectations */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-cyan-400" />
                Skills & Expectations
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Skills
                  </label>
                  {mode === 'edit' ? (
                    <textarea
                      name="skills"
                      value={formData.skills}
                      onChange={handleInputChange}
                      rows="3"
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                    />
                  ) : (
                    <p className="text-white/80 whitespace-pre-wrap">{participant.skills}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Expectations
                  </label>
                  {mode === 'edit' ? (
                    <textarea
                      name="expectations"
                      value={formData.expectations}
                      onChange={handleInputChange}
                      rows="3"
                      className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                    />
                  ) : (
                    <p className="text-white/80 whitespace-pre-wrap">{participant.expectations}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Team Information */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4">Team Information</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="has_team"
                    checked={formData.has_team}
                    onChange={handleInputChange}
                    disabled={mode === 'view'}
                    className="rounded border-white/20 bg-white/5 text-cyan-600 focus:ring-cyan-500 focus:ring-offset-0"
                  />
                  <label className="ml-2 text-sm text-white/70">Has a team</label>
                </div>

                {formData.has_team && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-white/70 mb-2">
                        Team Name
                      </label>
                      {mode === 'edit' ? (
                        <input
                          type="text"
                          name="team_name"
                          value={formData.team_name}
                          onChange={handleInputChange}
                          className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                        />
                      ) : (
                        <p className="text-white/80">{participant.team_name || 'Not specified'}</p>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-white/70 mb-2">
                          Team Member 1
                        </label>
                        {mode === 'edit' ? (
                          <input
                            type="text"
                            name="team_member_1"
                            value={formData.team_member_1}
                            onChange={handleInputChange}
                            placeholder="Email or name"
                            className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                          />
                        ) : (
                          <p className="text-white/80">{participant.team_member_1 || 'Not specified'}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-white/70 mb-2">
                          Team Member 2
                        </label>
                        {mode === 'edit' ? (
                          <input
                            type="text"
                            name="team_member_2"
                            value={formData.team_member_2}
                            onChange={handleInputChange}
                            placeholder="Email or name"
                            className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                          />
                        ) : (
                          <p className="text-white/80">{participant.team_member_2 || 'Not specified'}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-white/70 mb-2">
                          Team Member 3
                        </label>
                        {mode === 'edit' ? (
                          <input
                            type="text"
                            name="team_member_3"
                            value={formData.team_member_3}
                            onChange={handleInputChange}
                            placeholder="Email or name"
                            className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                          />
                        ) : (
                          <p className="text-white/80">{participant.team_member_3 || 'Not specified'}</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Registration Date */}
            <div>
              <h3 className="text-lg font-medium text-white mb-2">Registration Date</h3>
              <p className="text-white/70">
                {new Date(participant.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-white/10">
          <motion.button
            onClick={onClose}
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="px-6 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white font-medium rounded-xl text-sm transition-colors"
          >
            {mode === 'edit' ? 'Cancel' : 'Close'}
          </motion.button>
          {mode === 'edit' && (
            <motion.button
              onClick={handleSave}
              disabled={loading}
              whileHover={{ y: -2, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center gap-2 px-6 py-2.5 bg-gradient-to-r from-cyan-600 to-fuchsia-600 hover:from-cyan-500 hover:to-fuchsia-500 text-white font-semibold rounded-xl text-sm shadow-lg shadow-cyan-500/20 disabled:opacity-50 transition-all"
            >
              <Save className="w-4 h-4" />
              {loading ? 'Saving...' : 'Save Changes'}
            </motion.button>
          )}
        </div>
      </motion.div>
    </div>
  );
}
