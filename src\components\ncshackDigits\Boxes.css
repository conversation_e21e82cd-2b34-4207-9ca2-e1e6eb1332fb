.boxes-container {
  width: 100%;
  padding-top: 20px;
}

.box {
  background-color: #ffffff;
  border-radius: 12px;
    box-shadow: 0 0 8px #C05AA2, 0 0 12px #C05AA2;
  padding: 12px;
  color: rgb(0, 0, 0);
  width: 100%;
  max-width: 220px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease;
  margin-bottom: 16px;
}

.box:hover {
  transform: scale(1.05);
  background-color: #ffffff;
  box-shadow: 0 0 12px #C05AA2, 0 0 24px #C05AA2, 0 0 36px #C05AA2;
}



.boxes-wrapper {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

@media (min-width: 480px) {
  .boxes-container {
    padding-top: 30px;
  }

  .box {
    padding: 14px;
    max-width: 240px;
  }

  .box-icon {
    width: 40px;
    height: 40px;
  }

  .box-icon svg {
    width: 24px;
    height: 24px;
  }

  .box h3 {
    font-size: 18px;
  }

  .box-number {
    font-size: 36px;
  }

  .boxes-title h2 {
    font-size: 32px;
  }
}

@media (min-width: 768px) {
  .boxes-container {
    padding-top: 60px;
  }

  .boxes-wrapper {
    gap: 30px;
  }

  .boxes-title {
    margin-bottom: 60px;
  }

  .boxes-title h2 {
    font-size: 36px;
  }

  .boxes-title p {
    font-size: 17px;
  }
}

@media (min-width: 1024px) {
  .boxes-container {
    padding-top: 70px;
  }

  .boxes-wrapper {
    gap: 55px;
  }

  .boxes-title {
    margin-bottom: 80px;
  }

  .boxes-title h2 {
    font-size: 40px;
  }

  .boxes-title p {
    font-size: 18px;
  }
}

.box-icon {
  width: 36px;
  height: 36px;
  background-color: #40C6F0;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}
.box h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.box-number {
  font-size: 45px;
  font-weight: bold;
  margin-bottom: 5px;
}


.box h2{
    font-size: 35px;
  font-weight: bold;
}
.box p {
  font-size: 11px;
  color: #000000;
  line-height: 1.4;
}
.box-icon svg {
  color: #ffffff;
  width: 20px;
  height: 20px;
}

@media (max-width: 640px) {
  .boxes-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .box {
    width: 100%;
    max-width: 220px;
    margin-bottom: 0;
  }

  .box-icon {
    margin-bottom: 8px;
  }

  .box h3 {
    margin-bottom: 4px;
  }
}

.boxes-dots {
  display: flex;
  justify-content: center;
  gap: 30px; 
  margin-top: 50px; 
}

.dot1 {
  width: 63px;
  height: 63px;
  background-color: #451980;
  border-radius: 50%;
}

.dot2 {
  width: 63px;
  height: 63px;
  background-color: #9a5686;
  border-radius: 50%;
}

.dot3 {
  width: 63px;
  height: 63px;
  background-color: #3bb4d9;
  border-radius: 50%;
}