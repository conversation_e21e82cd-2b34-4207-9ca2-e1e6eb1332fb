// RegistrationClosed.jsx
"use client";

import { useState, useEffect } from "react";
import {
  Lock,
  Calendar,
  X,
  Mail,
  MessageCircle,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { REGISTRATION_CLOSED_CONFIG } from "../../config/registration";

export default function RegistrationClosed({ onClose }) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed z-50 inset-0 flex items-center justify-center p-4"
        >
          <motion.div
            onClick={handleClose}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.98 }}
            transition={{ type: "spring", damping: 25, stiffness: 500 }}
            className="relative w-full max-w-lg mt-4 max-h-screen overflow-y-auto rounded-3xl"
          >
            <div className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl">
              {/* Header */}
              <div className="p-6 sm:p-8 border-b border-white/10 bg-gradient-to-r from-cyan-900/10 to-fuchsia-900/10">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-300 to-fuchsia-300">
                      {REGISTRATION_CLOSED_CONFIG.title}
                    </h2>
                    <div className="flex items-center mt-1 text-cyan-100/80 text-sm">
                      <Calendar className="w-4 h-4 mr-2" /> {REGISTRATION_CLOSED_CONFIG.subtitle}
                    </div>
                  </div>
                  <button
                    onClick={handleClose}
                    className="p-1 rounded-full hover:bg-white/10 transition-colors text-white/60 hover:text-white ml-2"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-6 sm:p-8">
                <div className="text-center space-y-6">
                  {/* Lock Icon with Animation */}
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                    className="relative"
                  >
                    <motion.div
                      animate={{ 
                        scale: [1, 1.1, 1],
                        rotate: [0, 5, -5, 0]
                      }}
                      transition={{ 
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="w-20 h-20 mx-auto bg-gradient-to-br from-cyan-500/20 to-fuchsia-500/20 rounded-full flex items-center justify-center border border-white/10"
                    >
                      <Lock className="text-cyan-400 w-10 h-10" />
                    </motion.div>
                    
                    {/* Floating particles */}
                    {[...Array(6)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 bg-gradient-to-r from-cyan-400 to-fuchsia-400 rounded-full"
                        style={{
                          top: `${20 + Math.sin(i) * 30}%`,
                          left: `${20 + Math.cos(i) * 30}%`,
                        }}
                        animate={{
                          y: [0, -10, 0],
                          opacity: [0.3, 1, 0.3],
                        }}
                        transition={{
                          duration: 2 + i * 0.5,
                          repeat: Infinity,
                          ease: "easeInOut",
                        }}
                      />
                    ))}
                  </motion.div>

                  {/* Main Message */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="space-y-4"
                  >
                    <h3 className="text-2xl font-bold text-white">
                      {REGISTRATION_CLOSED_CONFIG.mainMessage}
                    </h3>
                    <p className="text-white/80 text-lg leading-relaxed">
                      {REGISTRATION_CLOSED_CONFIG.description}
                    </p>
                  </motion.div>

                  {/* Info Cards */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="space-y-4"
                  >
                    {/* Event Info */}
                    <div className="bg-gradient-to-r from-cyan-900/20 to-fuchsia-900/20 border border-white/10 rounded-xl p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <Calendar className="w-5 h-5 text-cyan-400" />
                        <span className="text-white font-semibold">Event Details</span>
                      </div>
                      <p className="text-white/70 text-sm">
                        <span className="text-cyan-300">{REGISTRATION_CLOSED_CONFIG.eventDates}</span> at {REGISTRATION_CLOSED_CONFIG.eventLocation}
                      </p>
                    </div>

                    {/* Contact Info */}
                    <div className="bg-gradient-to-r from-fuchsia-900/20 to-cyan-900/20 border border-white/10 rounded-xl p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <MessageCircle className="w-5 h-5 text-fuchsia-400" />
                        <span className="text-white font-semibold">Stay Connected</span>
                      </div>
                      <p className="text-white/70 text-sm mb-3">
                        Follow us for updates about future events and opportunities.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <motion.a
                          href={`mailto:${REGISTRATION_CLOSED_CONFIG.contactEmail}`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white/80 hover:text-white transition-all text-sm"
                        >
                          <Mail className="w-4 h-4" />
                          Contact Us
                        </motion.a>
                      </div>
                    </div>
                  </motion.div>

                  {/* Tagline */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8 }}
                    className="pt-4"
                  >
                    <p className="text-sm font-extrabold text-center">
                      <span className="text-cyan-400">{REGISTRATION_CLOSED_CONFIG.tagline.part1}</span> {REGISTRATION_CLOSED_CONFIG.tagline.part2}, <span className="text-fuchsia-400">{REGISTRATION_CLOSED_CONFIG.tagline.part3}</span> {REGISTRATION_CLOSED_CONFIG.tagline.part4}
                    </p>
                  </motion.div>

                  {/* Close Button */}
                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 }}
                    onClick={handleClose}
                    whileHover={{ 
                      scale: 1.05,
                      boxShadow: "0 20px 40px rgba(139, 92, 246, 0.3)"
                    }}
                    whileTap={{ scale: 0.95 }}
                    className="group relative px-8 py-3 rounded-2xl bg-gradient-to-r from-cyan-600 via-purple-600 to-fuchsia-600 text-white font-bold text-base shadow-2xl overflow-hidden transition-all duration-300 hover:shadow-cyan-500/25"
                  >
                    {/* Animated background gradient */}
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-600 via-purple-600 to-fuchsia-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    {/* Glass morphism overlay */}
                    <div className="absolute inset-0 bg-white/10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    {/* Button content */}
                    <span className="relative z-10 flex items-center gap-2">
                      <span className="tracking-wide">Close</span>
                    </span>
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
