'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { LogOut, User, Settings } from 'lucide-react';
import { useHrAuth } from '@/contexts/HrAuthContext';
import { motion } from 'framer-motion';
import { createPortal } from 'react-dom';

export default function DashboardHeader() {
  const { hrUser, signOut } = useHrAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });
  const buttonRef = useRef(null);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleUserMenuToggle = () => {
    if (!showUserMenu && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 8,
        right: window.innerWidth - rect.right
      });
    }
    setShowUserMenu(!showUserMenu);
  };

  useEffect(() => {
    const handleResize = () => {
      if (showUserMenu && buttonRef.current) {
        const rect = buttonRef.current.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + 8,
          right: window.innerWidth - rect.right
        });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [showUserMenu]);

  return (
    <header className="bg-gradient-to-r from-cyan-900/20 to-fuchsia-900/20 border-b border-white/10 shadow-2xl backdrop-blur-xl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <Image
              src="/Group.png"
              alt="NCS Hack Logo"
              width={120}
              height={40}
              className="h-10 w-auto"
            />
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-300 to-fuchsia-300">
                HR Dashboard
              </h1>
              <p className="text-sm text-cyan-100/80">Participant Management System</p>
            </div>
          </div>

          {/* User Menu */}
          <div className="relative">
            <motion.button
              ref={buttonRef}
              onClick={handleUserMenuToggle}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-3 text-white hover:text-cyan-300 transition-colors"
            >
              <div className="text-right hidden sm:block">
                <p className="text-sm font-medium text-white">{hrUser?.full_name}</p>
                <p className="text-xs text-cyan-100/80">{hrUser?.role}</p>
              </div>
              <div className="w-8 h-8 bg-gradient-to-r from-cyan-600 to-fuchsia-600 rounded-full flex items-center justify-center shadow-lg shadow-cyan-500/20">
                <User className="w-4 h-4 text-white" />
              </div>
            </motion.button>

          </div>
        </div>
      </div>

      {/* Portal for dropdown menu */}
      {showUserMenu && typeof window !== 'undefined' && createPortal(
        <>
          <div
            className="fixed inset-0 z-[95]"
            onClick={() => setShowUserMenu(false)}
          />
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="fixed w-48 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl z-[100]"
            style={{
              top: dropdownPosition.top,
              right: dropdownPosition.right
            }}
          >
            <div className="py-1">
              <div className="px-4 py-2 border-b border-white/10 bg-gradient-to-r from-cyan-900/10 to-fuchsia-900/10">
                <p className="text-sm font-medium text-white">{hrUser?.full_name}</p>
                <p className="text-xs text-cyan-100/80">{hrUser?.email}</p>
              </div>

              <motion.button
                onClick={() => setShowUserMenu(false)}
                whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                className="flex items-center w-full px-4 py-2 text-sm text-white/80 hover:text-white transition-colors"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </motion.button>

              <motion.button
                onClick={handleSignOut}
                whileHover={{ backgroundColor: 'rgba(239, 68, 68, 0.1)' }}
                className="flex items-center w-full px-4 py-2 text-sm text-red-400 hover:text-red-300 transition-colors"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </motion.button>
            </div>
          </motion.div>
        </>,
        document.body
      )}
    </header>
  );
}
