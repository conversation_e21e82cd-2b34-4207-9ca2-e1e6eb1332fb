.text-title {
  font-size: 28px;
  font-weight: 800;
  text-align: center;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 8px;
  padding: 0 10px;
}

.text-highlight {
  color: #2642b5;
}

.text-content {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  margin: 10px auto;
  max-width: 90%;
  line-height: 1.5;
}

@media (min-width: 480px) {
  .text-title {
    font-size: 42px;
  }

  .text-content {
    font-size: 17px;
  }
}

@media (min-width: 768px) {
  .text-title {
    font-size: 50px;
  }

  .text-content {
    font-size: 18px;
    max-width: 80%;
  }
}

@media (min-width: 1024px) {
  .text-title {
    font-size: 60px;
  }

  .text-content {
    font-size: 19.2px;
    max-width: 70%;
  }
}

.text-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.title-symbol {
  width: 180px;
  height: 90px;
  object-fit: contain;
  margin-right: 40px;
}
