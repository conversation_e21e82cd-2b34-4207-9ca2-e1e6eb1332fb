"use client";

import React, { useState } from "react";
import Hero from "@/components/Hero/Hero.jsx";
import AboutUs from "@/components/AboutUs/AboutUs.jsx";
import OurSponsors from "@/components/OurSponsors/OurSonsors.jsx";
import FAQ from "@/components/FAQ/FAQ.jsx";
import RegistrationForm from "@/components/form/RegistrationForm.jsx";
import Digits from "@/components/ncshackDigits/NcshackDigits.jsx";
import ScheduleNcs from "@/components/ScheduleNcs/ScheduleNcs.jsx";
import WhatIsNcshack from "@/components/What/Whatis";
import Footer from "@/components/Footer/Footer.jsx";
import { AnimatePresence, motion } from "framer-motion";
      


export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <main>
        <Hero onRegisterClick={() => setIsModalOpen(true)} />
        <AboutUs />
        <Digits />
        <WhatIsNcshack/>
        <ScheduleNcs />
        <OurSponsors />
        <FAQ />
      </main>

      <Footer />

      {/* Modal for Registration */}
      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            className="fixed inset-0 z-50 bg-black bg-opacity-70 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white p-6 rounded-lg w-full max-w-md relative"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 50, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <button
                onClick={() => setIsModalOpen(false)}
                className="absolute top-2 right-3 text-xl font-bold text-gray-800"
              >
                ×
              </button>
              <RegistrationForm />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
