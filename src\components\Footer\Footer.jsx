import {
  <PERSON>a<PERSON>nstagram,
  FaD<PERSON>rd,
  FaLinkedin,
  FaTiktok,
  FaEnvelope,
} from "react-icons/fa";

export default function Footer() {
  console.log("Footer component is rendering!"); // Debug log
  return (
    <footer className="bg-white text-black px-6 md:px-20 py-10 border-t-8  min-h-[300px]" style={{backgroundColor: '#ffffff', color: '#000000'}}>
      
      <div className="flex flex-col md:flex-row justify-between items-start gap-10">
        {/* Left: Logo with decoration */}
        <div className="relative w-fit">
          <img src="/images/ncshacklogoblack.svg" alt="NCSHACK Logo" className="w-48 md:w-64" />

          {/* Decorations (positioned relative to the logo) */}
          {/* Top right line */}
          <div className="absolute top-2 right-0 w-20 h-2  rounded-full"></div>
          <div className="absolute top-4 left-12 w-4 h-2 rounded-full"></div>

          {/* Bottom lines */}
          <div className="absolute bottom-0 left-0 w-14 h-2  rounded-full"></div>
          <div className="absolute bottom-1 left-20 w-4 h-2  rounded-full"></div>
        </div>

        {/* Right: Contact Us */}
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-blue-900">Contact Us:</h3>

          {/* Email */}
          <div className="flex items-center space-x-3">
            <FaEnvelope className="text-2xl" />
            <a
              href="mailto:<EMAIL>"
              className="text-lg font-medium underline-offset-4 hover:underline"
            >
              <EMAIL>
            </a>
          </div>

          {/* Social Icons */}
          <div className="flex space-x-6 text-2xl pt-2">
            <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">
              <FaInstagram />
            </a>
            <a href="https://discord.gg" target="_blank" rel="noopener noreferrer">
              <FaDiscord />
            </a>
            <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer">
              <FaLinkedin />
            </a>
            <a href="https://tiktok.com" target="_blank" rel="noopener noreferrer">
              <FaTiktok />
            </a>
          </div>
        </div>
      </div>

      {/* Bottom Line */}
      <div className="mt-10 border-t border-pink-300 pt-4 text-sm text-center">
        © 2025 NCSHACK. All rights reserved. Organized by{" "}
        <a
          href="https://www.linkedin.com/company/numidia-computer-society/"
          target="_blank"
          rel="noopener noreferrer"
          className="font-semibold underline hover:text-blue-700"
        >
          Numidia Computer Society
        </a>
      </div>
    </footer>
  );
}
