# Registration Control Documentation

## Overview

The NCS Hack 2025 registration system includes a frontend-only toggle mechanism to switch between open and closed registration states. This allows you to easily control when registration is available without modifying any backend APIs or database logic.

## How It Works

The system uses a configuration file (`src/config/registration.js`) that controls whether the registration form or the "Registration Closed" component is displayed.

## Quick Setup

### To Close Registration:

1. Open `src/config/registration.js`
2. Change `REGISTRATION_OPEN` from `true` to `false`
3. Deploy the changes

```javascript
export const REGISTRATION_OPEN = false; // Registration is now closed
```

### To Open Registration:

1. Open `src/config/registration.js`
2. Change `REGISTRATION_OPEN` from `false` to `true`
3. Deploy the changes

```javascript
export const REGISTRATION_OPEN = true; // Registration is now open
```

## Customizing the Closed Registration Message

You can customize the "Registration Closed" message by editing the `REGISTRATION_CLOSED_CONFIG` object in `src/config/registration.js`:

```javascript
export const REGISTRATION_CLOSED_CONFIG = {
  title: "Registration Closed",
  subtitle: "NCS Hack 2025",
  mainMessage: "Registration is Currently Closed",
  description: "Thank you for your interest in NCS Hack 2025! Registration has closed, but we appreciate your enthusiasm.",
  eventDates: "June 26-29, 2025",
  eventLocation: "Numidia Institute of Technology",
  contactEmail: "<EMAIL>",
  tagline: {
    part1: "CODE",
    part2: "HARD",
    part3: "DREAM", 
    part4: "BIG"
  }
};
```

## Features of the Closed Registration State

When registration is closed, users will see:

✅ **Professional Design**: Matches the NCS Hack 2025 visual theme
- Cyan-600 to fuchsia-600 gradient color scheme
- Dark theme with black background
- Consistent Geist fonts and typography
- Same hover effects and animations

✅ **Clear Messaging**: 
- Lock icon with animated particles
- "Registration Closed" title
- Event information (dates and location)
- Contact information for inquiries

✅ **User Experience**:
- Same modal positioning and behavior
- Close button (X) functionality
- Graceful messaging that doesn't feel like an error
- Professional branding with "Code Hard, Dream Big" tagline

## Technical Implementation

### Files Modified:
- `src/config/registration.js` - Configuration file
- `src/components/form/RegistrationForm.jsx` - Main form component
- `src/components/form/RegistrationClosed.jsx` - Closed state component

### Key Features:
- **Frontend-only**: No backend changes required
- **Easy toggle**: Single boolean flag controls the state
- **Configurable**: All text and settings can be customized
- **Consistent design**: Matches the existing website theme
- **Responsive**: Works on all device sizes

## Deployment

After making changes to the configuration:

1. **For Vercel**: Push changes to your repository, and Vercel will automatically deploy
2. **For other platforms**: Build and deploy according to your platform's process

```bash
npm run build
# Deploy the built files to your hosting platform
```

## Testing

To test the registration states locally:

1. **Test Open State**: Set `REGISTRATION_OPEN = true` and run `npm run dev`
2. **Test Closed State**: Set `REGISTRATION_OPEN = false` and run `npm run dev`
3. Click the "Register Now" button to see the respective state

## Support

If you need to modify the design or functionality further, the main components are located in:
- `src/components/form/RegistrationForm.jsx`
- `src/components/form/RegistrationClosed.jsx`
- `src/config/registration.js`

The system maintains all existing functionality when registration is open and provides a professional, branded experience when registration is closed.
