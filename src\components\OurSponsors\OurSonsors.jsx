"use client";

import Image from "next/image";
import { useEffect, useState } from "react";

const OurSponsors = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 200);
    return () => clearTimeout(timer);
  }, []);

  return (
    <section
      id="oursponsors"
      className="relative flex flex-col items-center justify-center min-h-[70vh] px-6 sm:px-12 lg:px-24 py-16 bg-black overflow-hidden"
    >
      {/* Radial Background */}
      <div
        className="absolute inset-0 z-0 pointer-events-none"
        style={{
          background:
            "radial-gradient(circle at center, rgba(80,0,120,0.2) 0%, rgba(0,0,0,0.9) 40%, black 100%)",
        }}
      />

      {/* Title with Animated White Lines */}
      <div className="group flex items-center justify-center gap-6 mb-8 z-10">
        <span className="block w-16 sm:w-24 h-[2px] bg-white shadow-white/50 rounded-full transition-all duration-500 group-hover:w-24 sm:group-hover:w-32 lg:group-hover:w-40" />
        <h1 className="text-white font-extrabold text-[clamp(2.2rem,6vw,4rem)] text-center tracking-tight transition-transform duration-300 group-hover:scale-105">
          Our Sponsors
        </h1>
        <span className="block w-16 sm:w-24 h-[2px] bg-white shadow-white/50 rounded-full transition-all duration-500 group-hover:w-24 sm:group-hover:w-32 lg:group-hover:w-40" />
      </div>

      {/* Subtitle */}
      <h2 className="text-white text-lg sm:text-2xl text-center font-semibold mb-12 z-10 transition-all duration-300 cursor-pointer">
        Proudly sponsored by our host
        <br />
        institution
      </h2>

      {/* Logo Section */}
      <div className="relative z-10 w-full flex justify-center items-center px-6 max-w-[900px]">
        {/* Glow Background - Using CSS instead of missing image */}
        <div className="absolute inset-0 flex justify-center items-center z-0 pointer-events-none">
          <div className="w-[400px] sm:w-[500px] md:w-[600px] h-[400px] sm:h-[500px] md:h-[600px] bg-gradient-radial from-blue-500/20 via-cyan-500/10 to-transparent blur-3xl opacity-70 animate-pulse rounded-full">
          </div>
        </div>

        {/* Logo Card */}
        <div
          className={`relative z-10 w-full max-w-[95%] sm:max-w-[85%] md:max-w-[75%] h-[180px] sm:h-[220px] md:h-[260px] bg-cover bg-center rounded-3xl flex items-center justify-center p-6 sm:p-10 transition-all duration-700 ease-out shadow-2xl hover:shadow-[0_0_80px_rgba(91,207,255,0.4)] hover:scale-105 ${
            isVisible ? "opacity-100 scale-100" : "opacity-0 scale-90"
          }`}
          style={{
            backgroundImage:
              "url('/{736B40C3-A98A-4BD0-B12B-DCFA6C461D97}.png')",
          }}
        >
          <Image
            src="/nit logo.png"
            alt="Numidia Institute of Technology Logo"
            width={320}
            height={160}
            className="object-contain max-w-[320px] sm:max-w-[360px] drop-shadow-lg"
          />
        </div>
      </div>
    </section>
  );
};

export default OurSponsors;
