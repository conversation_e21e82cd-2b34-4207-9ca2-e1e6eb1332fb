// RegistrationForm.jsx
"use client";

import { useState, useEffect } from "react";
import {
  CheckCircle,
  Calendar,
  X,
  Users,
  User,
  Mail,
  Phone,
  BookOpen,
  Hash,
  Code,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const fieldIcons = {
  firstName: <User className="w-5 h-5" />,
  lastName: <User className="w-5 h-5" />,
  email: <Mail className="w-5 h-5" />,
  phone: <Phone className="w-5 h-5" />,
  discordTag: <Hash className="w-5 h-5" />,
  university: <BookOpen className="w-5 h-5" />,
  studentId: <Hash className="w-5 h-5" />,
  skills: <Code className="w-5 h-5" />,
  teamName: <Users className="w-5 h-5" />,
};

export default function RegistrationForm({ onClose }) {
  const [hasTeam, setHasTeam] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const form = e.target;
    const requiredInputs = form.querySelectorAll("input[required], textarea[required], select[required]");
    const errors = {};
    let hasError = false;

    requiredInputs.forEach((input) => {
      if (!input.value.trim()) {
        errors[input.name] = "This field is required";
        hasError = true;
      }
    });

    if (hasError) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData(form);
      const data = Object.fromEntries(formData.entries());
      const response = await fetch("/api/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      if (!response.ok) throw new Error(result.error || "Registration failed");

      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 5000);
    } catch (error) {
      console.error("Registration error:", error);
      setFormErrors({ general: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed z-50 inset-0 flex items-center justify-center p-4"
        >
          <motion.div
            onClick={handleClose}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.98 }}
            transition={{ type: "spring", damping: 25, stiffness: 500 }}
            className="relative w-full max-w-lg mt-4 max-h-screen overflow-y-auto rounded-3xl"
          >
            <div className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl">
              <div className="p-6 sm:p-8 border-b border-white/10 bg-gradient-to-r from-cyan-900/10 to-fuchsia-900/10">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-300 to-fuchsia-300">
                      Register for NCS Hack
                    </h2>
                    <div className="flex items-center mt-1 text-cyan-100/80 text-sm">
                      <Calendar className="w-4 h-4 mr-2" /> 29 June 2025
                    </div>
                  </div>
                  <button
                    onClick={handleClose}
                    className="p-1 rounded-full hover:bg-white/10 transition-colors text-white/60 hover:text-white ml-2"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6 sm:p-8">
                {showSuccess ? (
                  <motion.div
                    className="text-center space-y-6 py-8"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <motion.div
                      animate={{ scale: [1, 1.1, 1], rotate: [0, 10, -10, 0] }}
                      transition={{ duration: 0.8 }}
                    >
                      <CheckCircle className="text-cyan-400 w-16 h-16 mx-auto" />
                    </motion.div>
                    <h2 className="text-3xl font-bold text-white">You're Registered!</h2>
                    <p className="text-white/80 text-lg">
                      Thank you for signing up for{" "}
                      <span className="text-cyan-300 font-semibold">NCS Hack</span>!
                    </p>
                    <motion.button
                      onClick={handleClose}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-8 py-3 bg-gradient-to-r from-cyan-600 to-fuchsia-600 hover:brightness-110 text-white rounded-full font-semibold shadow-lg"
                    >
                      Close
                    </motion.button>
                  </motion.div>
                ) : (
                  <form className="space-y-6" onSubmit={handleSubmit}>
                    {formErrors.general && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mb-6 p-4 rounded-xl bg-red-600/20 border border-red-400/50 text-white"
                      >
                        {formErrors.general}
                      </motion.div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                      {[
                        "firstName",
                        "lastName",
                        "email",
                        "phone",
                        "discordTag",
                        "university",
                        "studentId",
                        "skills",
                      ].map((field) => (
                        <motion.div key={field} whileHover={{ y: -2 }} className="flex flex-col">
                          <label className="text-sm font-medium text-white/70 mb-1.5 flex items-center gap-2">
                            {fieldIcons[field]}
                            {field.replace(/([A-Z])/g, " $1")} <span className="text-red-400">*</span>
                          </label>
                          <div className="relative">
                            <input
                              type={field === "email" ? "email" : "text"}
                              name={field}
                              placeholder={
                                field === "phone"
                                  ? "Enter your phone number"
                                  : `Enter your ${field.replace(/([A-Z])/g, " $1").toLowerCase()}`
                              }
                              required
                              className={`w-full bg-white/5 border ${
                                formErrors[field]
                                  ? "border-red-400/70"
                                  : "border-white/10"
                              } rounded-xl px-4 pl-10 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent text-sm transition-all duration-200`}
                            />
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40">
                              {fieldIcons[field]}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    <motion.div whileHover={{ y: -2 }}>
                      <label className="text-sm font-medium text-white/70 mb-1.5 flex items-center gap-2">
                        <BookOpen className="w-5 h-5" />
                        What are you expecting from this event?
                        <span className="text-red-400">*</span>
                      </label>
                      <textarea
                        name="expectations"
                        required
                        placeholder="Tell us what you're excited about..."
                        rows="3"
                        className={`w-full bg-white/5 border ${
                          formErrors.expectations
                            ? "border-red-400/70"
                            : "border-white/10"
                        } rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent text-sm transition-all duration-200`}
                      ></textarea>
                    </motion.div>

                    <motion.div whileHover={{ y: -2 }}>
                      <label className="text-sm font-medium text-white/70 mb-1.5 flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        Do you have a team?
                        <span className="text-red-400">*</span>
                      </label>
                      <select
                        name="hasTeam"
                        required
                        onChange={(e) => setHasTeam(e.target.value === "yes")}
                        className="w-full bg-white/10 text-white border border-white/10 rounded-xl px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent text-sm appearance-none"
                      >
                        <option value="" className="bg-slate-900 text-white">Select an option</option>
                        <option value="no" className="bg-slate-900 text-white">No, I need a team</option>
                        <option value="yes" className="bg-slate-900 text-white">Yes, I have a team</option>
                      </select>
                    </motion.div>

                    <AnimatePresence>
                      {hasTeam && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          className="space-y-4 overflow-hidden"
                        >
                          <motion.div whileHover={{ y: -2 }} className="pt-2">
                            <label className="text-sm font-medium text-white/70 mb-1.5 flex items-center gap-2">
                              <Users className="w-5 h-5" />
                              Team Name <span className="text-red-400">*</span>
                            </label>
                            <input
                              type="text"
                              name="teamName"
                              required
                              placeholder="Enter your team name"
                              className={`w-full bg-white/5 border ${
                                formErrors.teamName
                                  ? "border-red-400/70"
                                  : "border-white/10"
                              } rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent text-sm`}
                            />
                          </motion.div>
                          {[1, 2, 3].map((i) => (
                            <motion.div key={i} whileHover={{ y: -2 }}>
                              <input
                                type="text"
                                name={`teamMember${i}`}
                                placeholder={`Team member ${i} full name`}
                                className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent text-sm"
                              />
                            </motion.div>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>

                    <div className="flex flex-col sm:flex-row sm:justify-end gap-3 pt-6">
                      <motion.button
                        type="button"
                        onClick={handleClose}
                        whileHover={{ y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white font-medium rounded-xl text-sm transition-colors"
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        type="submit"
                        disabled={isSubmitting}
                        whileHover={{ y: -2, scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="px-6 py-2.5 bg-gradient-to-r from-cyan-600 to-fuchsia-600 hover:from-cyan-500 hover:to-fuchsia-500 text-white font-semibold rounded-xl text-sm flex items-center justify-center gap-2 shadow-lg shadow-cyan-500/20"
                      >
                        {isSubmitting ? (
                          <>
                            <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-white border-r-transparent"></span>
                            Processing...
                          </>
                        ) : (
                          "Register"
                        )}
                      </motion.button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
