{"name": "ncshack2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.50.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.514.0", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.17"}}