'use client';

import { useState, useEffect } from 'react';
import {
  DndContext,
  DragOverlay,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Users, UserPlus, Trash2 } from 'lucide-react';
import { useDashboard } from '@/contexts/DashboardContext';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';
import { motion } from 'framer-motion';

// Create admin client for team operations that bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhkbnhneXBjZnZoa2xqbHphZ3ZjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDMyNjQ5NSwiZXhwIjoyMDY1OTAyNDk1fQ.wWBwuigDLWQegzcyGcxMs51sVcWIyiTl8HYnQUd7pwY'
);

// Draggable Participant Component
function DraggableParticipant({ participant, isOverlay = false }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: participant.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`p-3 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-lg shadow-sm cursor-grab active:cursor-grabbing hover:shadow-cyan-500/20 transition-all backdrop-blur-sm ${
        isOverlay ? 'rotate-3 scale-105' : ''
      }`}
    >
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-r from-cyan-600 to-fuchsia-600 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-lg shadow-cyan-500/20">
          {participant.first_name[0]}{participant.last_name[0]}
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-white truncate">
            {participant.first_name} {participant.last_name}
          </div>
          <div className="text-xs text-white/60 truncate">
            {participant.university}
          </div>
        </div>
      </div>
    </div>
  );
}

// Droppable Team Component
function DroppableTeam({ team, participants, onRemoveFromTeam }) {
  const teamMembers = participants.filter(p => 
    team.team_members?.some(member => member.participant_id === p.id)
  );

  return (
    <div className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-white">{team.name}</h3>
          <div className="flex items-center gap-2 text-sm text-white/60">
            <Users className="w-4 h-4" />
            {teamMembers.length}/{team.max_members} members
          </div>
        </div>
      </div>

      <SortableContext items={teamMembers.map(p => p.id)} strategy={verticalListSortingStrategy}>
        <div className="space-y-2 min-h-[100px] border-2 border-dashed border-white/20 rounded-lg p-3 bg-white/5">
          {teamMembers.length > 0 ? (
            teamMembers.map((participant) => (
              <div key={participant.id} className="relative group">
                <DraggableParticipant participant={participant} />
                <motion.button
                  onClick={() => onRemoveFromTeam(team.id, participant.id)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="absolute top-1 right-1 p-1 bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                  title="Remove from team"
                >
                  <Trash2 className="w-3 h-3" />
                </motion.button>
              </div>
            ))
          ) : (
            <div className="flex items-center justify-center h-20 text-white/40">
              <div className="text-center">
                <UserPlus className="w-8 h-8 mx-auto mb-2" />
                <p className="text-sm">Drop participants here</p>
              </div>
            </div>
          )}
        </div>
      </SortableContext>
    </div>
  );
}

export default function DragDropTeamAssignment() {
  const { participants, teams, loadTeams, loadParticipants } = useDashboard();
  const [activeId, setActiveId] = useState(null);
  const [loading, setLoading] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    loadTeams();
    loadParticipants();
  }, []);

  // Get unassigned participants
  const unassignedParticipants = participants.filter(participant => 
    !teams.some(team => 
      team.team_members?.some(member => member.participant_id === participant.id)
    )
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const draggedParticipant = participants.find(p => p.id === active.id);
    if (!draggedParticipant) return;

    // Check if dropped on a team
    const targetTeam = teams.find(team => 
      team.team_members?.some(member => member.participant_id === over.id) ||
      over.id === `team-${team.id}`
    );

    if (targetTeam) {
      // Check if team has space
      const currentMembers = targetTeam.team_members?.length || 0;
      if (currentMembers >= targetTeam.max_members) {
        alert(`Team "${targetTeam.name}" is full (${targetTeam.max_members}/${targetTeam.max_members})`);
        return;
      }

      setLoading(true);
      try {
        // Remove from current team if any
        const currentTeam = teams.find(team => 
          team.team_members?.some(member => member.participant_id === draggedParticipant.id)
        );

        if (currentTeam) {
          await supabaseAdmin
            .from('team_members')
            .delete()
            .eq('team_id', currentTeam.id)
            .eq('participant_id', draggedParticipant.id);
        }

        // Add to new team
        const { error } = await supabaseAdmin
          .from('team_members')
          .insert([{
            team_id: targetTeam.id,
            participant_id: draggedParticipant.id,
            role: 'member'
          }]);

        if (error) throw error;

        // Reload data
        await loadTeams();
      } catch (error) {
        console.error('Error moving participant:', error);
        alert('Failed to move participant: ' + error.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleRemoveFromTeam = async (teamId, participantId) => {
    setLoading(true);
    try {
      const { error } = await supabaseAdmin
        .from('team_members')
        .delete()
        .eq('team_id', teamId)
        .eq('participant_id', participantId);

      if (error) throw error;

      await loadTeams();
    } catch (error) {
      console.error('Error removing participant from team:', error);
      alert('Failed to remove participant from team: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const activeParticipant = participants.find(p => p.id === activeId);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Drag & Drop Team Assignment</h2>
        {loading && (
          <div className="flex items-center gap-2 text-sm text-cyan-300">
            <div className="animate-spin h-4 w-4 border-2 border-cyan-400 border-r-transparent rounded-full"></div>
            Updating...
          </div>
        )}
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Unassigned Participants */}
          <div className="lg:col-span-1">
            <div className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-6">
              <h3 className="text-lg font-medium text-white mb-4">
                Unassigned Participants ({unassignedParticipants.length})
              </h3>

              <SortableContext items={unassignedParticipants.map(p => p.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {unassignedParticipants.map((participant) => (
                    <DraggableParticipant key={participant.id} participant={participant} />
                  ))}
                  {unassignedParticipants.length === 0 && (
                    <div className="text-center py-8 text-white/50">
                      <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>All participants are assigned to teams</p>
                    </div>
                  )}
                </div>
              </SortableContext>
            </div>
          </div>

          {/* Teams */}
          <div className="lg:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {teams.map((team) => (
                <DroppableTeam
                  key={team.id}
                  team={team}
                  participants={participants}
                  onRemoveFromTeam={handleRemoveFromTeam}
                />
              ))}

              {teams.length === 0 && (
                <div className="col-span-2 text-center py-12 text-white/50">
                  <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg">No teams created yet</p>
                  <p className="text-sm">Create teams in the Team Management tab</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <DragOverlay>
          {activeParticipant ? (
            <DraggableParticipant participant={activeParticipant} isOverlay />
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
