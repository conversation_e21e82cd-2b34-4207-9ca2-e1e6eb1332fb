'use client';

import { useState, useRef, useEffect } from 'react';
import { Search, Filter, X, Download, Plus, ChevronDown } from 'lucide-react';
import { useDashboard } from '@/contexts/DashboardContext';
import { exportParticipantsToCSV, exportByUniversity, exportByTeamStatus, exportStatistics } from '@/lib/csvExport';
import { motion } from 'framer-motion';
import { createPortal } from 'react-dom';

export default function SearchAndFilters({ onAddParticipant, onExportData }) {
  const { filters, setFilters, participants, selectedParticipants, teams } = useDashboard();
  const [showFilters, setShowFilters] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });
  const exportButtonRef = useRef(null);

  const handleSearchChange = (e) => {
    setFilters({ search: e.target.value });
  };

  const handleFilterChange = (key, value) => {
    setFilters({ [key]: value });
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      university: '',
      teamName: '',
      teamStatus: 'all'
    });
  };

  const hasActiveFilters = filters.search || filters.university || filters.teamName || filters.teamStatus !== 'all';

  // Get unique universities for filter dropdown
  const universities = [...new Set(participants.map(p => p.university))].sort();

  const teamStatusOptions = [
    { value: 'all', label: 'All Participants' },
    { value: 'no_team', label: 'No Team' },
    { value: 'has_team', label: 'Has Team' },
    { value: 'team_incomplete', label: 'Incomplete Teams' },
    { value: 'team_complete', label: 'Complete Teams' }
  ];

  const handleExportAll = () => {
    exportParticipantsToCSV(participants);
    setShowExportMenu(false);
  };

  const handleExportSelected = () => {
    if (selectedParticipants.length === 0) {
      alert('Please select participants to export');
      return;
    }
    const selectedData = participants.filter(p => selectedParticipants.includes(p.id));
    exportParticipantsToCSV(selectedData, 'selected_participants.csv');
    setShowExportMenu(false);
  };

  const handleExportByUniversity = () => {
    if (!filters.university) {
      alert('Please select a university filter first');
      return;
    }
    exportByUniversity(participants, filters.university);
    setShowExportMenu(false);
  };

  const handleExportByTeamStatus = () => {
    if (filters.teamStatus === 'all') {
      alert('Please select a specific team status filter first');
      return;
    }
    const hasTeam = filters.teamStatus === 'has_team';
    exportByTeamStatus(participants, hasTeam);
    setShowExportMenu(false);
  };

  const handleExportStatistics = () => {
    exportStatistics(participants, teams);
    setShowExportMenu(false);
  };

  const handleExportMenuToggle = () => {
    if (!showExportMenu && exportButtonRef.current) {
      const rect = exportButtonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 8,
        right: window.innerWidth - rect.right
      });
    }
    setShowExportMenu(!showExportMenu);
  };

  useEffect(() => {
    const handleResize = () => {
      if (showExportMenu && exportButtonRef.current) {
        const rect = exportButtonRef.current.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + 8,
          right: window.innerWidth - rect.right
        });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [showExportMenu]);

  return (
    <div className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-6 mb-6">
      {/* Main Search and Action Bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-4">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 w-4 h-4" />
            <input
              type="text"
              placeholder="Search participants..."
              value={filters.search}
              onChange={handleSearchChange}
              className="w-full bg-white/5 border border-white/10 rounded-xl px-4 pl-10 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
            />
          </div>
        </div>

        <div className="flex items-center gap-3">
          <motion.button
            onClick={() => setShowFilters(!showFilters)}
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.95 }}
            className={`flex items-center gap-2 px-4 py-2.5 rounded-xl border transition-all ${
              showFilters || hasActiveFilters
                ? 'bg-cyan-600/20 border-cyan-400/50 text-cyan-300'
                : 'bg-white/5 border-white/10 text-white/70 hover:bg-white/10 hover:text-white'
            }`}
          >
            <Filter className="w-4 h-4" />
            Filters
            {hasActiveFilters && (
              <span className="bg-gradient-to-r from-cyan-600 to-fuchsia-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {Object.values(filters).filter(v => v && v !== 'all').length}
              </span>
            )}
          </motion.button>

          <div className="relative">
            <motion.button
              ref={exportButtonRef}
              onClick={handleExportMenuToggle}
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-xl hover:from-emerald-500 hover:to-teal-500 transition-all shadow-lg shadow-emerald-500/20"
            >
              <Download className="w-4 h-4" />
              Export
              <ChevronDown className="w-4 h-4" />
            </motion.button>
          </div>

          <motion.button
            onClick={onAddParticipant}
            whileHover={{ y: -2, scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-cyan-600 to-fuchsia-600 text-white rounded-xl hover:from-cyan-500 hover:to-fuchsia-500 transition-all shadow-lg shadow-cyan-500/20"
          >
            <Plus className="w-4 h-4" />
            Add Participant
          </motion.button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="border-t border-white/10 pt-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-white/70 mb-2">
                University
              </label>
              <select
                value={filters.university}
                onChange={(e) => handleFilterChange('university', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-xl px-3 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
              >
                <option value="" className="bg-gray-800 text-white">All Universities</option>
                {universities.map(university => (
                  <option key={university} value={university} className="bg-gray-800 text-white">
                    {university}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-white/70 mb-2">
                Team Name
              </label>
              <input
                type="text"
                placeholder="Filter by team name..."
                value={filters.teamName}
                onChange={(e) => handleFilterChange('teamName', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-xl px-3 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white/70 mb-2">
                Team Status
              </label>
              <select
                value={filters.teamStatus}
                onChange={(e) => handleFilterChange('teamStatus', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-xl px-3 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
              >
                {teamStatusOptions.map(option => (
                  <option key={option.value} value={option.value} className="bg-gray-800 text-white">
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {hasActiveFilters && (
            <div className="mt-4 flex justify-end">
              <motion.button
                onClick={clearFilters}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center gap-2 px-3 py-1 text-sm text-white/60 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
                Clear Filters
              </motion.button>
            </div>
          )}
        </motion.div>
      )}

      {/* Portal for export dropdown */}
      {showExportMenu && typeof window !== 'undefined' && createPortal(
        <>
          <div
            className="fixed inset-0 z-[85]"
            onClick={() => setShowExportMenu(false)}
          />
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed w-56 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl backdrop-blur-xl rounded-xl z-[90]"
            style={{
              top: dropdownPosition.top,
              right: dropdownPosition.right,
              maxHeight: '400px',
              overflowY: 'auto'
            }}
          >
            <div className="py-1">
              <motion.button
                onClick={handleExportAll}
                whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                className="w-full text-left px-4 py-2 text-sm text-white/80 hover:text-white transition-colors"
              >
                Export All Participants
              </motion.button>

              {selectedParticipants.length > 0 && (
                <motion.button
                  onClick={handleExportSelected}
                  whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  className="w-full text-left px-4 py-2 text-sm text-white/80 hover:text-white transition-colors"
                >
                  Export Selected ({selectedParticipants.length})
                </motion.button>
              )}

              {filters.university && (
                <motion.button
                  onClick={handleExportByUniversity}
                  whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  className="w-full text-left px-4 py-2 text-sm text-white/80 hover:text-white transition-colors"
                >
                  Export {filters.university} Students
                </motion.button>
              )}

              {filters.teamStatus !== 'all' && (
                <motion.button
                  onClick={handleExportByTeamStatus}
                  whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  className="w-full text-left px-4 py-2 text-sm text-white/80 hover:text-white transition-colors"
                >
                  Export by Team Status
                </motion.button>
              )}

              <div className="border-t border-white/10 my-1"></div>

              <motion.button
                onClick={handleExportStatistics}
                whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                className="w-full text-left px-4 py-2 text-sm text-white/80 hover:text-white transition-colors"
              >
                Export Statistics
              </motion.button>
            </div>
          </motion.div>
        </>,
        document.body
      )}
    </div>
  );
}
