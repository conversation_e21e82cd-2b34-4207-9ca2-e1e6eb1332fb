'use client';

import { useState } from 'react';
import { DashboardProvider, useDashboard } from '@/contexts/DashboardContext';
import ErrorBoundary from '@/components/ErrorBoundary';
import { ToastContainer, useToast } from '@/components/Toast';
import DashboardHeader from './components/DashboardHeader';
import StatsCards from './components/StatsCards';
import SearchAndFilters from './components/SearchAndFilters';
import ParticipantsTable from './components/ParticipantsTable';
import TeamManagement from './components/TeamManagement';
import ParticipantModal from './components/ParticipantModal';
import DragDropTeamAssignment from './components/DragDropTeamAssignment';
import BulkOperations from './components/BulkOperations';
import { exportParticipantsToCSV, exportTeamsToCSV, exportSelectedParticipants, exportStatistics } from '@/lib/csvExport';

function DashboardContent() {
  const { selectedParticipants, setSelectedParticipants } = useDashboard();
  const { toasts, removeToast, success, error } = useToast();
  const [activeTab, setActiveTab] = useState('participants');
  const [selectedParticipant, setSelectedParticipant] = useState(null);
  const [showParticipantModal, setShowParticipantModal] = useState(false);
  const [modalMode, setModalMode] = useState('view');

  const handleAddParticipant = () => {
    // For now, redirect to registration form
    window.open('/', '_blank');
  };

  const handleEditParticipant = (participant) => {
    setSelectedParticipant(participant);
    setModalMode('edit');
    setShowParticipantModal(true);
  };

  const handleViewParticipant = (participant) => {
    setSelectedParticipant(participant);
    setModalMode('view');
    setShowParticipantModal(true);
  };

  const handleExportData = () => {
    const { participants, teams, selectedParticipants } = useDashboard();

    if (selectedParticipants.length > 0) {
      exportSelectedParticipants(participants, selectedParticipants);
    } else {
      exportParticipantsToCSV(participants);
    }
  };

  const handleExportTeams = () => {
    const { teams } = useDashboard();
    exportTeamsToCSV(teams);
  };

  const handleExportStats = () => {
    const { participants, teams } = useDashboard();
    exportStatistics(participants, teams);
  };

  const handleClearSelection = () => {
    setSelectedParticipants([]);
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <DashboardHeader />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <StatsCards />

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-white/10">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('participants')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-all ${
                  activeTab === 'participants'
                    ? 'border-cyan-400 text-cyan-300'
                    : 'border-transparent text-white/60 hover:text-white hover:border-white/30'
                }`}
              >
                Participants
              </button>
              <button
                onClick={() => setActiveTab('teams')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-all ${
                  activeTab === 'teams'
                    ? 'border-cyan-400 text-cyan-300'
                    : 'border-transparent text-white/60 hover:text-white hover:border-white/30'
                }`}
              >
                Teams
              </button>
              <button
                onClick={() => setActiveTab('assignment')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-all ${
                  activeTab === 'assignment'
                    ? 'border-cyan-400 text-cyan-300'
                    : 'border-transparent text-white/60 hover:text-white hover:border-white/30'
                }`}
              >
                Team Assignment
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'participants' && (
          <>
            <SearchAndFilters
              onAddParticipant={handleAddParticipant}
              onExportData={handleExportData}
            />

            <ParticipantsTable
              onEditParticipant={handleEditParticipant}
              onViewParticipant={handleViewParticipant}
            />
          </>
        )}

        {activeTab === 'teams' && (
          <TeamManagement />
        )}

        {activeTab === 'assignment' && (
          <DragDropTeamAssignment />
        )}
      </main>

      {/* Participant Modal */}
      <ParticipantModal
        participant={selectedParticipant}
        isOpen={showParticipantModal}
        onClose={() => setShowParticipantModal(false)}
        mode={modalMode}
      />

      {/* Bulk Operations */}
      <BulkOperations
        selectedParticipants={selectedParticipants}
        onClearSelection={handleClearSelection}
      />

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </div>
  );
}

export default function Dashboard() {
  return (
    <ErrorBoundary>
      <DashboardProvider>
        <DashboardContent />
      </DashboardProvider>
    </ErrorBoundary>
  );
}