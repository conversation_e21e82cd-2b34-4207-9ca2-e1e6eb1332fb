// Registration Configuration
// This file controls the registration state for the NCS Hack 2025 event

/**
 * Registration Configuration
 * 
 * Set REGISTRATION_OPEN to:
 * - true: Show the normal registration form
 * - false: Show the "Registration Closed" state
 * 
 * This is a frontend-only toggle that doesn't affect any backend APIs
 * or database logic. It simply changes what component is rendered.
 */
export const REGISTRATION_OPEN = true;

/**
 * Registration Closed Message Configuration
 * You can customize the closed registration message here
 */
export const REGISTRATION_CLOSED_CONFIG = {
  title: "Registration Closed",
  subtitle: "NCS Hack 2025",
  mainMessage: "Registration is Currently Closed",
  description: "Thank you for your interest in NCS Hack 2025! Registration has closed, but we appreciate your enthusiasm.",
  eventDates: "June 26-29, 2025",
  eventLocation: "Numidia Institute of Technology",
  contactEmail: "<EMAIL>",
  tagline: {
    part1: "CODE",
    part2: "HARD",
    part3: "DREAM", 
    part4: "BIG"
  }
};

/**
 * How to use:
 * 
 * 1. To close registration:
 *    - Change REGISTRATION_OPEN to false
 *    - Deploy the changes
 * 
 * 2. To open registration:
 *    - Change REGISTRATION_OPEN to true
 *    - Deploy the changes
 * 
 * 3. To customize the closed message:
 *    - Modify the REGISTRATION_CLOSED_CONFIG object above
 */
