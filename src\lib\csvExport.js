// CSV Export Utilities

export const exportParticipantsToCSV = (participants, filename = 'participants.csv') => {
  const headers = [
    'First Name',
    'Last Name', 
    'Email',
    'Phone',
    'Discord Tag',
    'University',
    'Student ID',
    'Skills',
    'Expectations',
    'Has Team',
    'Team Name',
    'Team Member 1',
    'Team Member 2', 
    'Team Member 3',
    'Registration Date'
  ];

  const csvContent = [
    headers.join(','),
    ...participants.map(participant => [
      escapeCS<PERSON><PERSON>ield(participant.first_name),
      escapeCS<PERSON><PERSON><PERSON>(participant.last_name),
      escapeCSV<PERSON>ield(participant.email),
      escapeCSV<PERSON>ield(participant.phone),
      escapeCSV<PERSON>ield(participant.discord_tag || ''),
      escapeCS<PERSON><PERSON><PERSON>(participant.university),
      escapeCS<PERSON><PERSON>ield(participant.student_id),
      escapeCS<PERSON><PERSON><PERSON>(participant.skills || ''),
      escapeCS<PERSON><PERSON>ield(participant.expectations || ''),
      participant.has_team ? 'Yes' : 'No',
      escapeCSV<PERSON><PERSON>(participant.team_name || ''),
      escapeCSV<PERSON>ield(participant.team_member_1 || ''),
      escapeCSV<PERSON>ield(participant.team_member_2 || ''),
      escapeCSV<PERSON>ield(participant.team_member_3 || ''),
      new Date(participant.created_at).toLocaleDateString()
    ].join(','))
  ].join('\n');

  downloadCSV(csvContent, filename);
};

export const exportTeamsToCSV = (teams, filename = 'teams.csv') => {
  const headers = [
    'Team Name',
    'Description',
    'Max Members',
    'Current Members',
    'Status',
    'Created Date',
    'Members'
  ];

  const csvContent = [
    headers.join(','),
    ...teams.map(team => [
      escapeCSVField(team.name),
      escapeCSVField(team.description || ''),
      team.max_members,
      team.current_members || 0,
      escapeCSVField(team.status || 'active'),
      new Date(team.created_at).toLocaleDateString(),
      escapeCSVField(
        team.team_members?.map(member => 
          `${member.participants?.first_name} ${member.participants?.last_name}`
        ).join('; ') || ''
      )
    ].join(','))
  ].join('\n');

  downloadCSV(csvContent, filename);
};

export const exportSelectedParticipants = (participants, selectedIds, filename = 'selected_participants.csv') => {
  const selectedParticipants = participants.filter(p => selectedIds.includes(p.id));
  exportParticipantsToCSV(selectedParticipants, filename);
};

export const exportByUniversity = (participants, university, filename) => {
  const universityParticipants = participants.filter(p => p.university === university);
  const defaultFilename = `${university.replace(/\s+/g, '_').toLowerCase()}_participants.csv`;
  exportParticipantsToCSV(universityParticipants, filename || defaultFilename);
};

export const exportByTeamStatus = (participants, hasTeam, filename) => {
  const filteredParticipants = participants.filter(p => p.has_team === hasTeam);
  const defaultFilename = hasTeam ? 'participants_with_teams.csv' : 'participants_without_teams.csv';
  exportParticipantsToCSV(filteredParticipants, filename || defaultFilename);
};

export const exportTeamMembers = (team, filename) => {
  if (!team.team_members || team.team_members.length === 0) {
    alert('This team has no members to export.');
    return;
  }

  const headers = [
    'First Name',
    'Last Name',
    'Email',
    'Phone',
    'University',
    'Student ID',
    'Role in Team',
    'Joined Date'
  ];

  const csvContent = [
    headers.join(','),
    ...team.team_members.map(member => [
      escapeCSVField(member.participants?.first_name || ''),
      escapeCSVField(member.participants?.last_name || ''),
      escapeCSVField(member.participants?.email || ''),
      escapeCSVField(member.participants?.phone || ''),
      escapeCSVField(member.participants?.university || ''),
      escapeCSVField(member.participants?.student_id || ''),
      escapeCSVField(member.role || 'member'),
      new Date(member.joined_at).toLocaleDateString()
    ].join(','))
  ].join('\n');

  const defaultFilename = `${team.name.replace(/\s+/g, '_').toLowerCase()}_members.csv`;
  downloadCSV(csvContent, filename || defaultFilename);
};

// Utility function to escape CSV fields
const escapeCSVField = (field) => {
  if (field === null || field === undefined) return '';
  
  const stringField = String(field);
  
  // If field contains comma, newline, or quote, wrap in quotes and escape quotes
  if (stringField.includes(',') || stringField.includes('\n') || stringField.includes('"')) {
    return `"${stringField.replace(/"/g, '""')}"`;
  }
  
  return stringField;
};

// Utility function to download CSV
const downloadCSV = (csvContent, filename) => {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Function to append to existing CSV (for bulk operations)
export const appendToCSV = (existingCSVContent, newParticipants) => {
  const newRows = newParticipants.map(participant => [
    escapeCSVField(participant.first_name),
    escapeCSVField(participant.last_name),
    escapeCSVField(participant.email),
    escapeCSVField(participant.phone),
    escapeCSVField(participant.discord_tag || ''),
    escapeCSVField(participant.university),
    escapeCSVField(participant.student_id),
    escapeCSVField(participant.skills || ''),
    escapeCSVField(participant.expectations || ''),
    participant.has_team ? 'Yes' : 'No',
    escapeCSVField(participant.team_name || ''),
    escapeCSVField(participant.team_member_1 || ''),
    escapeCSVField(participant.team_member_2 || ''),
    escapeCSVField(participant.team_member_3 || ''),
    new Date(participant.created_at).toLocaleDateString()
  ].join(','));

  return existingCSVContent + '\n' + newRows.join('\n');
};

// Function to append selected participants to an existing CSV file
export const appendSelectedParticipantsToFile = (participants, selectedIds, onFileSelect) => {
  const selectedParticipants = participants.filter(p => selectedIds.includes(p.id));

  if (selectedParticipants.length === 0) {
    alert('No participants selected for export.');
    return;
  }

  // Create file input element
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = '.csv';
  fileInput.style.display = 'none';

  fileInput.onchange = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check file size (limit to 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File is too large. Please select a CSV file smaller than 10MB.');
      document.body.removeChild(fileInput);
      return;
    }

    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      alert('Please select a CSV file (.csv extension).');
      document.body.removeChild(fileInput);
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const existingContent = e.target.result;

        // Basic validation - check if content looks like CSV
        if (!existingContent || typeof existingContent !== 'string') {
          throw new Error('Invalid file content');
        }

        const updatedContent = appendToCSV(existingContent, selectedParticipants);

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const originalName = file.name.replace('.csv', '');
        const newFilename = `${originalName}_updated_${timestamp}.csv`;

        downloadCSV(updatedContent, newFilename);

        if (onFileSelect) {
          onFileSelect(selectedParticipants.length, newFilename);
        }
      } catch (error) {
        console.error('Error processing CSV file:', error);
        alert('Error processing the CSV file. Please make sure it\'s a valid CSV file and try again.');
      }
    };

    reader.onerror = () => {
      alert('Error reading the file. Please try again.');
    };

    reader.readAsText(file, 'UTF-8');

    // Clean up
    document.body.removeChild(fileInput);
  };

  fileInput.oncancel = () => {
    // Clean up if user cancels
    document.body.removeChild(fileInput);
  };

  // Trigger file selection
  document.body.appendChild(fileInput);
  fileInput.click();
};

// Statistics export
export const exportStatistics = (participants, teams, filename = 'registration_statistics.csv') => {
  const stats = {
    'Total Participants': participants.length,
    'Participants with Teams': participants.filter(p => p.has_team).length,
    'Participants without Teams': participants.filter(p => !p.has_team).length,
    'Total Teams': teams.length,
    'Complete Teams': teams.filter(t => t.current_members === t.max_members).length,
    'Incomplete Teams': teams.filter(t => t.current_members < t.max_members).length,
    'Universities Represented': [...new Set(participants.map(p => p.university))].length
  };

  const csvContent = [
    'Statistic,Value',
    ...Object.entries(stats).map(([key, value]) => `${escapeCSVField(key)},${value}`)
  ].join('\n');

  downloadCSV(csvContent, filename);
};
