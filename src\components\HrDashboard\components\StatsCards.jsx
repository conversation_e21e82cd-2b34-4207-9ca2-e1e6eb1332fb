'use client';

import { Users, UserCheck, UserX, Trophy } from 'lucide-react';
import { useDashboard } from '@/contexts/DashboardContext';
import { motion } from 'framer-motion';

export default function StatsCards() {
  const { participants, teams } = useDashboard();

  const stats = [
    {
      title: 'Total Participants',
      value: participants.length,
      icon: Users,
      color: 'from-cyan-600 to-fuchsia-600',
      bgColor: 'bg-gradient-to-br from-cyan-900/20 to-fuchsia-900/20',
      textColor: 'text-cyan-300'
    },
    {
      title: 'With Teams',
      value: participants.filter(p => p.has_team).length,
      icon: UserCheck,
      color: 'from-cyan-500 to-cyan-600',
      bgColor: 'bg-gradient-to-br from-cyan-900/20 to-cyan-800/20',
      textColor: 'text-cyan-400'
    },
    {
      title: 'Without Teams',
      value: participants.filter(p => !p.has_team).length,
      icon: UserX,
      color: 'from-fuchsia-500 to-fuchsia-600',
      bgColor: 'bg-gradient-to-br from-fuchsia-900/20 to-fuchsia-800/20',
      textColor: 'text-fuchsia-400'
    },
    {
      title: 'Total Teams',
      value: teams.length,
      icon: Trophy,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-gradient-to-br from-purple-900/20 to-purple-800/20',
      textColor: 'text-purple-400'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          whileHover={{ y: -2, scale: 1.02 }}
          className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-6 hover:shadow-cyan-500/20 transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-white/70 mb-1">
                {stat.title}
              </p>
              <p className="text-3xl font-bold text-white">
                {stat.value}
              </p>
            </div>
            <div className={`p-3 rounded-lg ${stat.bgColor} backdrop-blur-sm`}>
              <stat.icon className={`w-6 h-6 ${stat.textColor}`} />
            </div>
          </div>

          {/* Progress bar for visual appeal */}
          <div className="mt-4">
            <div className="w-full bg-white/10 rounded-full h-2">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${Math.min((stat.value / Math.max(...stats.map(s => s.value))) * 100, 100)}%` }}
                transition={{ delay: index * 0.1 + 0.3, duration: 0.8 }}
                className={`h-2 rounded-full bg-gradient-to-r ${stat.color} shadow-lg`}
              />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
