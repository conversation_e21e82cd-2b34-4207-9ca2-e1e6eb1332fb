"use client"

export default function ScheduleBackground() {
  return (
    <div className="absolute inset-0 overflow-hidden rounded-3xl">
      {/*black background */}
      <div className="absolute inset-0 bg-black" />

      {/*  colored overlay */}
      <div className="absolute inset-0 bg-gradient-to-tr from-[#3BC2EC]/5 via-transparent to-[#C05AA2]/5" />

      {/* animated rectangles - optimized for mobile */}
      <div className="absolute top-1/4 left-1/4 w-48 sm:w-64 md:w-80 h-16 sm:h-24 md:h-32 bg-[#2A9FCC] rounded-lg mix-blend-screen filter blur-sm opacity-30 sm:opacity-40 animate-tech-float-1" />

      <div className="absolute top-1/2 right-1/4 w-40 sm:w-52 md:w-64 h-20 sm:h-32 md:h-40 bg-[#A04A92] rounded-lg mix-blend-screen filter blur-sm opacity-30 sm:opacity-40 animate-tech-float-2" />

      <div className="absolute bottom-1/3 left-1/3 w-56 sm:w-72 md:w-96 h-12 sm:h-18 md:h-24 bg-[#2A9FCC] rounded-lg mix-blend-screen filter blur-md opacity-20 sm:opacity-30 animate-tech-float-3" />

      {/* Smaller tech elements */}
      <div className="absolute top-1/6 right-1/3 w-32 sm:w-40 md:w-48 h-8 sm:h-12 md:h-16 bg-[#A04A92] rounded-md mix-blend-screen filter blur-sm opacity-40 sm:opacity-50 animate-tech-float-4" />

      <div className="absolute bottom-1/4 left-1/6 w-20 sm:w-24 md:w-32 h-20 sm:h-24 md:h-32 bg-[#2A9FCC] rounded-lg mix-blend-screen filter blur-sm opacity-35 sm:opacity-45 animate-tech-float-5" />

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `
            linear-gradient(rgba(59, 194, 236, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 194, 236, 0.1) 1px, transparent 1px)
          `,
            backgroundSize: "20px 20px",
          }}
        />
      </div>

      {/* CSS Animation */}
      <style jsx>{`
        @keyframes tech-float-1 {
          0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
          33% { transform: translate(80px, -60px) rotate(5deg) scale(1.1); }
          66% { transform: translate(40px, -30px) rotate(-3deg) scale(1.05); }
        }

        @keyframes tech-float-2 {
          0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
          33% { transform: translate(-100px, 70px) rotate(-8deg) scale(0.9); }
          66% { transform: translate(-50px, 35px) rotate(4deg) scale(0.95); }
        }

        @keyframes tech-float-3 {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          33% { transform: translate(-60px, 80px) rotate(3deg); }
          66% { transform: translate(-30px, 40px) rotate(-2deg); }
        }

        @keyframes tech-float-4 {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          50% { transform: translate(50px, -40px) rotate(-5deg); }
        }

        @keyframes tech-float-5 {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          50% { transform: translate(-30px, 30px) rotate(8deg); }
        }

        .animate-tech-float-1 { animation: tech-float-1 18s ease-in-out infinite; }
        .animate-tech-float-2 { animation: tech-float-2 22s ease-in-out infinite; }
        .animate-tech-float-3 { animation: tech-float-3 25s ease-in-out infinite; }
        .animate-tech-float-4 { animation: tech-float-4 14s ease-in-out infinite; }
        .animate-tech-float-5 { animation: tech-float-5 16s ease-in-out infinite; }
      `}</style>
    </div>
  )
}
