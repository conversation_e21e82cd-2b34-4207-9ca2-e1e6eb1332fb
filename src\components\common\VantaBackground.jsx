function VantaBackground({ children, className = "" }) {
  const vantaRef = useRef(null);
  const [vantaEffect, setVantaEffect] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const loadVanta = async () => {
      if (!vantaRef.current || vantaEffect) return;

      if (!window.THREE) {
        await new Promise((resolve) => {
          const script = document.createElement("script");
          script.src = "https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js";
          script.onload = resolve;
          document.head.appendChild(script);
        });
      }

      if (!window.VANTA) {
        await new Promise((resolve) => {
          const script = document.createElement("script");
          script.src = "https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.globe.min.js";
          script.onload = resolve;
          document.head.appendChild(script);
        });
      }

      if (window.VANTA && window.THREE && isMounted) {
        const screenWidth = window.innerWidth;
        const globeSize = screenWidth < 640 ? 0.25 : 1.4; // EXTRA small on phones

        const effect = window.VANTA.GLOBE({
          el: vantaRef.current,
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          scale: 1.0,
          scaleMobile: 1.0,
          minHeight: 200.0,
          minWidth: 200.0,
          color: 0x9500aa,
          color2: 0xfa00be,
          size: globeSize,
          backgroundColor: 0x000000,
        });

        setVantaEffect(effect);
      }
    };

    loadVanta();

    const handleResize = () => {
      if (vantaEffect && typeof vantaEffect.resize === "function") {
        vantaEffect.resize();
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      isMounted = false;
      if (vantaEffect) vantaEffect.destroy();
      window.removeEventListener("resize", handleResize);
    };
  }, [vantaEffect]);

  return (
    <div className={`relative w-full min-h-screen ${className}`}>
      <div ref={vantaRef} className="absolute inset-0 z-0 w-full h-full" />
      <div className="relative z-10">{children}</div>
    </div>
  );
}
