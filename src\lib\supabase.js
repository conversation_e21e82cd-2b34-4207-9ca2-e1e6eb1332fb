import { createClient } from '@supabase/supabase-js'

// For client-side usage, we need NEXT_PUBLIC_ prefix
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Client for public operations (registration, etc.)
export const supabase = createClient(supabaseUrl, supabaseAnonKey)