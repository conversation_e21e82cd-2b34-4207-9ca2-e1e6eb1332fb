"use client";

import { useEffect, useRef, useState } from "react";
import { Plus, Minus } from "lucide-react";

function VantaBackground({ children, className = "" }) {
  const vantaRef = useRef(null);
  const [vantaEffect, setVantaEffect] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const loadVanta = async () => {
      if (!vantaRef.current || vantaEffect) return;

      if (!window.THREE) {
        await new Promise((resolve) => {
          const script = document.createElement("script");
          script.src = "https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js";
          script.onload = resolve;
          document.head.appendChild(script);
        });
      }

      if (!window.VANTA) {
        await new Promise((resolve) => {
          const script = document.createElement("script");
          script.src = "https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.globe.min.js";
          script.onload = resolve;
          document.head.appendChild(script);
        });
      }

      if (window.VANTA && window.THREE && isMounted) {
        const effect = window.VANTA.GLOBE({
          el: vantaRef.current,
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.0,
          minWidth: 200.0,
          color: 0x9500aa,
          color2: 0xfa00be,
          size: window.innerWidth < 640 ? 0.3 : 1.4,
          backgroundColor: 0x000000,
        });
        setVantaEffect(effect);
      }
    };

    loadVanta();

    const handleResize = () => {
      if (vantaEffect?.setOptions) {
        vantaEffect.setOptions({
          size: window.innerWidth < 640 ? 0.3 : 1.4,
        });
        vantaEffect.resize();
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      isMounted = false;
      if (vantaEffect) vantaEffect.destroy();
      window.removeEventListener("resize", handleResize);
    };
  }, [vantaEffect]);

  return (
    <div className={`relative w-full min-h-screen ${className}`}>
      <div ref={vantaRef} className="absolute inset-0 z-0 w-full h-full" />
      <div className="relative z-10">{children}</div>
    </div>
  );
}

export default function CustomFAQ() {
  const [openItem, setOpenItem] = useState(null);

  const toggleItem = (id) => {
    setOpenItem(openItem === id ? null : id);
  };

  const faqItems = [
    {
      id: "faq-1",
      question: "What is NCS Hack?",
      answer:
        "NCS Hack is a student-driven event that brings together tech enthusiasts to collaborate and solve problems through innovation and teamwork...",
    },
    {
      id: "faq-2",
      question: "When and where will the event take place?",
      answer:
        "The event will take place over four days at Numidia Institute of Technology located in Rahmania, Sidi Abdellah, Algiers...",
    },
    {
      id: "faq-3",
      question: "Who can participate in NCS Hack?",
      answer:
        "NCS Hack is open to developers, designers, and tech enthusiasts of all levels, from beginners to experts...",
    },
    {
      id: "faq-4",
      question: "How do I register for the event?",
      answer:
        'Registration is simple! Click on the "Register" button on our homepage...',
    },
    {
      id: "faq-5",
      question: "Do I need to have a team to participate?",
      answer:
        "No, you don't need to have a team beforehand. While you're welcome to participate with a pre-formed team...",
    },
    {
      id: "faq-6",
      question: "What kind of projects can we work on?",
      answer:
        "This is a hackathon event focused on solving tech challenges, building useful tools, and innovating...",
    },
    {
      id: "faq-7",
      question: "Are there any prizes for participants?",
      answer:
        "Yes! We have various prizes for winning teams in different categories...",
    },
    {
      id: "faq-8",
      question: "What resources will be provided during the event?",
      answer:
        "We'll provide access to mentors, technical resources, and support throughout the event...",
    },
  ];

  return (
    <VantaBackground>
      <div className="py-16 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center justify-center">
        <div className="max-w-4xl w-full">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-6xl font-extrabold text-white mb-4 animate-pulse">
              Frequently Asked Questions
            </h2>
            <p className="text-[#FFFFFF] font-bold text-lg max-w-2xl mx-auto">
              Everything you need to know about{" "}
              <span className="text-pink-500">NCS Hack</span> and how to
              participate.
            </p>
          </div>

          <div className="space-y-5">
            {faqItems.map((item) => (
              <div
                key={item.id}
                className="border border-white/20 bg-white/10 backdrop-blur-md rounded-xl shadow-lg hover:shadow-pink-500/30 transition transform hover:scale-[1.01]"
              >
                <button
                  onClick={() => toggleItem(item.id)}
                  className="flex justify-between items-center w-full py-6 text-left px-4"
                >
                  <span className="text-white font-semibold text-lg sm:text-xl">
                    {item.question}
                  </span>
                  <span
                    className={`text-pink-400 ml-2 transition-transform duration-300 ${
                      openItem === item.id ? "rotate-180 scale-110" : ""
                    }`}
                  >
                    {openItem === item.id ? (
                      <Minus className="h-5 w-5" />
                    ) : (
                      <Plus className="h-5 w-5" />
                    )}
                  </span>
                </button>

                <div
                  className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    openItem === item.id ? "max-h-96 pb-6 px-4" : "max-h-0"
                  }`}
                  style={{ transition: "max-height 0.5s ease, padding 0.3s ease" }}
                >
                  <p className="text-gray-100 pt-4">{item.answer}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </VantaBackground>
  );
}
