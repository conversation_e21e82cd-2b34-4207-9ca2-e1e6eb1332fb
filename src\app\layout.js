import { <PERSON>eist, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  metadataBase: new URL('https://ncshack20.vercel.app'),
  title: "NCS Hack 2025 | Code Hard, Dream Big",
  description: "Join Algeria's premier student hackathon! 4 days of innovation, collaboration, and coding at Numidia Institute of Technology. June 29-july2, 2025. Register now for an unforgettable tech experience.",
  keywords: ["hackathon", "algeria", "coding", "technology", "students", "innovation", "NCS Hack", "programming", "web development", "AI"],
  authors: [{ name: "NCS Hack Team" }],
  creator: "NCS Hack",
  publisher: "Numidia Institute of Technology",

  // Open Graph metadata for social media sharing
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ncshack25.vercel.app",
    siteName: "NCS Hack 2025",
    title: "NCS Hack 2025 | Code Hard, Dream Big",
    description: "Join Algeria's premier student hackathon! 4 days of innovation, collaboration, and coding at Numidia Institute of Technology. June 26-29, 2025.",
    images: [
      {
        url: "/preview.png",
        width: 1700,
        height: 622,
        alt: "NCS Hack 2025 - Algeria's Premier Student Hackathon",
        type: "image/png",
      },
    ],
  },

  // Twitter Card metadata
  twitter: {
    card: "summary_large_image",
    site: "@ncshack",
    creator: "@ncshack",
    title: "NCS Hack 2025 | Code Hard, Dream Big",
    description: "Join Algeria's premier student hackathon! 4 days of innovation, collaboration, and coding. June 26-29, 2025. Register now!",
    images: ["/preview.png"],
  },

  // Additional metadata for better SEO and social sharing
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },

  // Verification and other meta tags
  verification: {
    google: "your-google-verification-code", // Replace with actual verification code when available
  },

  // Additional meta tags
  other: {
    "theme-color": "#000000",
    "color-scheme": "dark",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "black-translucent",
    "format-detection": "telephone=no",
  },
};

// Add viewport configuration
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#000000',
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        {/* Additional meta tags for better social media sharing */}
        <link rel="canonical" href="https://ncshack25.vercel.app" />
        <meta name="application-name" content="NCS Hack 2025" />
        <meta name="apple-mobile-web-app-title" content="NCS Hack 2025" />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/preview.png" />

        {/* Preload critical resources */}
        <link rel="preload" href="/preview.png" as="image" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
