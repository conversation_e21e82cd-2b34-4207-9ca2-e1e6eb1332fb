'use client';

import { useHrAuth } from '@/contexts/HrAuthContext';
import HrLogin from './HrLogin';

export default function HrProtectedRoute({ children }) {
  const { isAuthenticated, loading, hrUser } = useHrAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-cyan-400 border-r-transparent"></div>
          <p className="text-white mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <HrLogin onLoginSuccess={() => {}} />;
  }

  return children;
}
