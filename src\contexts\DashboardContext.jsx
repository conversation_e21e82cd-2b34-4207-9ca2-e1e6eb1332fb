'use client';

import { createContext, useContext, useReducer, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

// Create admin client for HR operations that bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.wWBwuigDLWQegzcyGcxMs51sVcWIyiTl8HYnQUd7pwY'
);

const DashboardContext = createContext();

export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};

// Dashboard state reducer
const dashboardReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_PARTICIPANTS':
      return { ...state, participants: action.payload, loading: false };
    
    case 'ADD_PARTICIPANT':
      return { 
        ...state, 
        participants: [...state.participants, action.payload] 
      };
    
    case 'UPDATE_PARTICIPANT':
      return {
        ...state,
        participants: state.participants.map(p => 
          p.id === action.payload.id ? action.payload : p
        )
      };
    
    case 'DELETE_PARTICIPANT':
      return {
        ...state,
        participants: state.participants.filter(p => p.id !== action.payload)
      };
    
    case 'SET_TEAMS':
      return { ...state, teams: action.payload };
    
    case 'ADD_TEAM':
      return { 
        ...state, 
        teams: [...state.teams, action.payload] 
      };
    
    case 'UPDATE_TEAM':
      return {
        ...state,
        teams: state.teams.map(t => 
          t.id === action.payload.id ? action.payload : t
        )
      };
    
    case 'DELETE_TEAM':
      return {
        ...state,
        teams: state.teams.filter(t => t.id !== action.payload)
      };
    
    case 'SET_FILTERS':
      return { ...state, filters: { ...state.filters, ...action.payload } };
    
    case 'SET_SELECTED_PARTICIPANTS':
      return { ...state, selectedParticipants: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    default:
      return state;
  }
};

const initialState = {
  participants: [],
  teams: [],
  loading: true,
  error: null,
  filters: {
    search: '',
    university: '',
    teamName: '',
    teamStatus: 'all'
  },
  selectedParticipants: []
};

export function DashboardProvider({ children }) {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Load initial data
  useEffect(() => {
    loadParticipants();
    loadTeams();
  }, []);

  const loadParticipants = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const { data, error } = await supabaseAdmin
        .from('participants')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      dispatch({ type: 'SET_PARTICIPANTS', payload: data || [] });
    } catch (error) {
      console.error('Error loading participants:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const loadTeams = async () => {
    try {
      const { data, error } = await supabaseAdmin
        .from('teams')
        .select(`
          *,
          team_members(
            id,
            participant_id,
            role,
            joined_at,
            participants(
              id,
              first_name,
              last_name,
              email,
              university,
              student_id
            )
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      dispatch({ type: 'SET_TEAMS', payload: data || [] });
    } catch (error) {
      console.error('Error loading teams:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const addParticipant = async (participantData) => {
    try {
      const { data, error } = await supabase
        .from('participants')
        .insert([participantData])
        .select()
        .single();

      if (error) throw error;
      
      dispatch({ type: 'ADD_PARTICIPANT', payload: data });
      return data;
    } catch (error) {
      console.error('Error adding participant:', error);
      throw error;
    }
  };

  const updateParticipant = async (id, updates) => {
    try {
      const { data, error } = await supabaseAdmin
        .from('participants')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: 'UPDATE_PARTICIPANT', payload: data });
      return data;
    } catch (error) {
      console.error('Error updating participant:', error);
      throw error;
    }
  };

  const deleteParticipant = async (id) => {
    try {
      const { error } = await supabaseAdmin
        .from('participants')
        .delete()
        .eq('id', id);

      if (error) throw error;

      dispatch({ type: 'DELETE_PARTICIPANT', payload: id });
    } catch (error) {
      console.error('Error deleting participant:', error);
      throw error;
    }
  };

  const createTeam = async (teamData) => {
    try {
      const { data, error } = await supabaseAdmin
        .from('teams')
        .insert([teamData])
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: 'ADD_TEAM', payload: data });
      return data;
    } catch (error) {
      console.error('Error creating team:', error);
      throw error;
    }
  };

  const setFilters = (newFilters) => {
    dispatch({ type: 'SET_FILTERS', payload: newFilters });
  };

  const setSelectedParticipants = (participants) => {
    dispatch({ type: 'SET_SELECTED_PARTICIPANTS', payload: participants });
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value = {
    ...state,
    loadParticipants,
    loadTeams,
    addParticipant,
    updateParticipant,
    deleteParticipant,
    createTeam,
    setFilters,
    setSelectedParticipants,
    clearError
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
}
