// app/about/page.tsx

"use client";
import { motion } from "framer-motion";

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-black text-white p-6 flex flex-col items-center">
      <motion.h1
        className="text-6xl font-bold mb-12 text-center"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <span className="text-pink-400">About</span> <span className="text-white">us</span>
      </motion.h1>

      <div className="flex flex-col lg:flex-row gap-12 max-w-7xl w-full items-stretch">
        {/* Texte à gauche avec grande taille */}
        <motion.div
          className="lg:w-1/2 w-full flex flex-col justify-center space-y-6"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.9 }}
        >
          <p className="text-gray-300 text-2xl leading-loose">
            The <span className="font-bold text-white">NCS Club</span> is a student organization at
            <span className="font-bold text-white"> Numidia Institute Of Technology</span>,
            that provides an inclusive platform for tech enthusiasts.
          </p>
          <p className="text-gray-300 text-2xl leading-loose">
            It aims to boost their computer science skills. Through workshops and projects, they foster community growth.
          </p>
        </motion.div>

        {/* Image à droite, agrandie */}
        <motion.div
          className="lg:w-1/2 w-full flex items-center justify-center"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.9 }}
        >
          <motion.div
            className="rounded-[80px] overflow-hidden border-[6px] border-black w-full h-[520px]"
            whileHover={{ scale: 1.03 }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            <img
              src="/IMG_6996 (1) 1.png"
              alt="NCS Club Group Photo"
              className="w-full h-full object-cover"
            />
          </motion.div>
        </motion.div>
      </div>

      {/* Lien de localisation */}
      <motion.a
  href="https://www.google.com/maps/place/Rahmania/@36.6809086,2.9001446,19.69z/data=!4m6!3m5!1s0x128fa5ca66430005:0xf65540e40ba36818!8m2!3d36.6809676!4d2.9005518!16s%2Fg%2F11w9p9c9l6?entry=ttu&g_ep=EgoyMDI1MDYwOC4wIKXMDSoASAFQAw%3D%3D"
  target="_blank"
  rel="noopener noreferrer"
  className="mt-12 text-white text-lg font-semibold hover:text-pink-400 transition"
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  transition={{ delay: 1, duration: 0.6 }}
>
  view location &rarr;
</motion.a>

    </div>
  );
}
