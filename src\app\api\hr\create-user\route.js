import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { email, password, fullName } = body
    
    if (!email || !password || !fullName) {
      return NextResponse.json(
        { error: 'Email, password, and full name are required' },
        { status: 400 }
      )
    }

    // Create auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: email.toLowerCase().trim(),
      password: password,
      email_confirm: true
    })

    if (authError) {
      console.error('Auth user creation error:', authError)
      return NextResponse.json(
        { error: 'Failed to create user account' },
        { status: 500 }
      )
    }

    // Create HR user record
    const { data: hrUser, error: hrError } = await supabase
      .from('hr_users')
      .insert([{
        auth_user_id: authData.user.id,
        email: email.toLowerCase().trim(),
        full_name: fullName.trim(),
        role: 'hr_staff',
        is_active: true
      }])
      .select()
      .single()

    if (hrError) {
      console.error('HR user creation error:', hrError)
      // Try to clean up auth user if HR user creation fails
      try {
        await supabase.auth.admin.deleteUser(authData.user.id)
      } catch (cleanupError) {
        console.error('Cleanup error:', cleanupError)
      }
      
      return NextResponse.json(
        { error: 'Failed to create HR user record' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'HR user created successfully',
      user: {
        id: hrUser.id,
        email: hrUser.email,
        fullName: hrUser.full_name,
        role: hrUser.role
      }
    })

  } catch (error) {
    console.error('HR user creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// This endpoint should be protected in production
// For now, we'll add a simple check
export async function GET() {
  return NextResponse.json({
    message: 'HR User Creation Endpoint',
    note: 'Use POST method to create HR users',
    example: {
      email: '<EMAIL>',
      password: 'securepassword',
      fullName: 'HR Staff Name'
    }
  })
}
