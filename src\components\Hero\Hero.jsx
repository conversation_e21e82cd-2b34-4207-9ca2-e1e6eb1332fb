"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import RegistrationForm from "../form/RegistrationForm";

const floatVariant = {
  animate: {
    y: [0, -5, 0, 5, 0],
    transition: {
      duration: 4,
      repeat: Infinity,
      ease: "easeInOut",
    },
  },
};

const floatSideVariant = {
  animate: {
    x: [0, 3, 0, -3, 0],
    transition: {
      duration: 5,
      repeat: Infinity,
      ease: "easeInOut",
    },
  },
};

const hoverEffect = {
  whileHover: {
    scale: 1.2,
    filter: "drop-shadow(0px 0px 6px rgba(255,255,255,0.4))",
    rotate: [0, 10, -10, 0],
    transition: { duration: 0.6 },
  },
};

export default function HeroSection() {
  const [showRegistration, setShowRegistration] = useState(false);
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  useEffect(() => {
    const targetDate = new Date("2025-06-29T00:00:00");

    const updateTimeLeft = () => {
      const now = new Date();
      const difference = targetDate.getTime() - now.getTime();

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
        const minutes = Math.floor((difference / (1000 * 60)) % 60);
        const seconds = Math.floor((difference / 1000) % 60);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    };

    updateTimeLeft();
    const interval = setInterval(updateTimeLeft, 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <header className="relative w-full bg-black text-white flex flex-col items-center justify-start px-4 pt-24 pb-12">
      {/* Decorative Elements */}
      {/* Top Left */}
      <div className="absolute top-12 left-4 md:left-6 flex gap-[10px] sm:hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`sm-tl-${i}`}
            className="w-[2px] h-[20px] bg-fuchsia-500 rotate-[25deg]"
            variants={floatSideVariant}
            animate="animate"
            {...hoverEffect}
          />
        ))}
      </div>
      <div className="absolute top-12 left-4 md:left-6 hidden sm:flex md:hidden gap-[8px]">
        {[...Array(9)].map((_, i) => (
          <motion.div
            key={`md-tl-${i}`}
            className="w-[2.5px] h-[28px] bg-fuchsia-500 rotate-[25deg]"
            variants={floatSideVariant}
            animate="animate"
            {...hoverEffect}
          />
        ))}
      </div>
      <div className="absolute top-24 left-4 md:left-6 hidden md:flex gap-[6px]">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={`lg-tl-${i}`}
            className="w-[4px] h-[40px] bg-fuchsia-500 rotate-[25deg]"
            variants={floatSideVariant}
            animate="animate"
            {...hoverEffect}
          />
        ))}
      </div>

      {/* Top Right */}
      <div className="absolute top-6 right-4 md:right-6 flex gap-[10px] sm:hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`sm-tr-${i}`}
            className="w-[2px] h-[20px] bg-[#451980] rotate-[25deg]"
            variants={floatSideVariant}
            animate="animate"
            {...hoverEffect}
          />
        ))}
      </div>
      <div className="absolute top-6 right-4 md:right-6 hidden sm:flex md:hidden gap-[8px]">
        {[...Array(9)].map((_, i) => (
          <motion.div
            key={`md-tr-${i}`}
            className="w-[2.5px] h-[28px] bg-[#451980] rotate-[25deg]"
            variants={floatSideVariant}
            animate="animate"
            {...hoverEffect}
          />
        ))}
      </div>
      <div className="absolute top-6 right-4 md:right-6 hidden md:flex gap-[6px]">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={`lg-tr-${i}`}
            className="w-[4px] h-[40px] bg-[#451980] rotate-[25deg]"
            variants={floatSideVariant}
            animate="animate"
            {...hoverEffect}
          />
        ))}
      </div>

      {/* Logo */}
      <motion.div
        className="mt-4 w-full max-w-[90%] md:max-w-[600px]"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
      >
        <Image
          src="/ncslogo.png"
          alt="NCS Hack Logo"
          width={600}
          height={180}
          priority
          className="w-full h-auto"
          style={{ width: '100%', height: 'auto' }}
        />
      </motion.div>

      {/* Bottom Right Cyan Dashes */}
      <div className="absolute top-[260px] right-2 flex gap-[10px] sm:hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`sm-br-${i}`}
            className="w-[2px] h-[20px] bg-cyan-400 rotate-[25deg]"
            variants={floatSideVariant}
            animate="animate"
            {...hoverEffect}
          />
        ))}
      </div>

      {/* Bottom Left Crosses */}
      <div className="absolute left-2 md:left-6 top-[320px] md:top-[460px] flex flex-col gap-[2px] md:gap-[4px]">
        {["text-purple-500", "text-cyan-400", "text-fuchsia-500", "text-white"].map((color, idx) => (
          <motion.span
            key={`cross-${idx}`}
            className={`${color} text-3xl md:text-5xl font-bold`}
            variants={floatVariant}
            animate="animate"
            {...hoverEffect}
          >
            ×
          </motion.span>
        ))}
      </div>

      {/* Tagline + Register Button */}
      <motion.div
        className="text-center mt-4 w-full max-w-screen-sm px-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <p className="mt-[-40px] text-[13px] leading-[100%] font-extrabold text-center break-words">
          <span className="text-cyan-400">CODE</span> HARD, <span className="text-fuchsia-400">DREAM</span> BIG
        </p>
        <motion.button
          whileHover={{
            scale: 1.05,
            y: -2,
            boxShadow: "0 20px 40px rgba(139, 92, 246, 0.3)"
          }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowRegistration(true)}
          className="group relative mt-8 px-8 py-4 rounded-2xl bg-gradient-to-r from-cyan-600 via-purple-600 to-fuchsia-600 text-white font-bold text-base shadow-2xl overflow-hidden transition-all duration-300 hover:shadow-cyan-500/25"
        >
          {/* Animated background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-cyan-600 via-purple-600 to-fuchsia-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Glass morphism overlay */}
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Shimmer effect */}
          <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-700 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12" />

          {/* Button content */}
          <span className="relative z-10 flex items-center gap-2">
            <span className="tracking-wide">register now</span>
            <motion.span
              animate={{ x: [0, 4, 0] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              className="text-lg"
            >
              →
            </motion.span>
          </span>
        </motion.button>
        {showRegistration && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div className="relative">
              <button
                onClick={() => setShowRegistration(false)}
                className="absolute -top-10 right-0 text-white hover:text-gray-300"
              >
                ✕
              </button>
              <RegistrationForm onClose={() => setShowRegistration(false)} />
            </div>
          </div>
        )}
      </motion.div>

      {/* Countdown Timer */}
      <div className="mt-16 md:mt-28 flex gap-4 md:gap-8 flex-wrap justify-center">
        {[
          { label: "D", value: timeLeft.days, color: "border-purple-400" },
          { label: "H", value: timeLeft.hours, color: "border-fuchsia-500" },
          { label: "M", value: timeLeft.minutes, color: "border-cyan-400" },
          { label: "S", value: timeLeft.seconds, color: "border-pink-400" },
        ].map(({ label, value, color }) => (
          <motion.div
            key={label}
            className={`w-14 h-14 md:w-24 md:h-24 ${color} border rounded-full flex flex-col items-center justify-center text-sm md:text-2xl`}
            variants={floatVariant}
            animate="animate"
            whileHover={{ scale: 1.15, rotate: 5 }}
          >
            <span className="font-bold">{value}</span>
            <span className="text-[10px] md:text-xs mt-1">{label}</span>
          </motion.div>
        ))}
      </div>

      {/* Right Crosses */}
      <div className="absolute right-2 md:right-6 top-[320px] md:top-auto md:bottom-[100px] flex flex-col gap-[2px] md:gap-[1px] leading-none">
        {["text-purple-500", "text-cyan-400", "text-fuchsia-500", "text-white"].map((color, idx) => (
          <motion.div
            key={`brcross-${idx}`}
            className={`${color} text-3xl md:text-5xl font-bold leading-none`}
            variants={floatVariant}
            animate="animate"
            {...hoverEffect}
          >
            ×
          </motion.div>
        ))}
      </div>
    </header>
  );
}