'use client';

import { useState, useMemo } from 'react';
import { Edit, Trash2, Eye, Mail, Phone, ExternalLink } from 'lucide-react';
import { useDashboard } from '@/contexts/DashboardContext';
import { motion } from 'framer-motion';

export default function ParticipantsTable({ onEditParticipant, onViewParticipant }) {
  const { participants, filters, deleteParticipant, selectedParticipants, setSelectedParticipants } = useDashboard();
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');

  // Filter and sort participants
  const filteredParticipants = useMemo(() => {
    let filtered = participants.filter(participant => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const searchableFields = [
          participant.first_name,
          participant.last_name,
          participant.email,
          participant.university,
          participant.student_id,
          participant.team_name
        ].filter(Boolean);
        
        if (!searchableFields.some(field => 
          field.toLowerCase().includes(searchTerm)
        )) {
          return false;
        }
      }

      // University filter
      if (filters.university && participant.university !== filters.university) {
        return false;
      }

      // Team name filter
      if (filters.teamName && (!participant.team_name || 
          !participant.team_name.toLowerCase().includes(filters.teamName.toLowerCase()))) {
        return false;
      }

      // Team status filter
      if (filters.teamStatus !== 'all') {
        switch (filters.teamStatus) {
          case 'no_team':
            if (participant.has_team) return false;
            break;
          case 'has_team':
            if (!participant.has_team) return false;
            break;
          case 'team_incomplete':
            // This would need team member count logic
            break;
          case 'team_complete':
            // This would need team member count logic
            break;
        }
      }

      return true;
    });

    // Sort participants
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (sortField === 'name') {
        aValue = `${a.first_name} ${a.last_name}`;
        bValue = `${b.first_name} ${b.last_name}`;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [participants, filters, sortField, sortDirection]);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleSelectParticipant = (participantId, isSelected) => {
    if (isSelected) {
      setSelectedParticipants([...selectedParticipants, participantId]);
    } else {
      setSelectedParticipants(selectedParticipants.filter(id => id !== participantId));
    }
  };

  const handleSelectAll = (isSelected) => {
    if (isSelected) {
      setSelectedParticipants(filteredParticipants.map(p => p.id));
    } else {
      setSelectedParticipants([]);
    }
  };

  const handleDeleteParticipant = async (id) => {
    if (window.confirm('Are you sure you want to delete this participant?')) {
      try {
        await deleteParticipant(id);
      } catch (error) {
        alert('Failed to delete participant: ' + error.message);
      }
    }
  };

  const SortableHeader = ({ field, children }) => (
    <th
      onClick={() => handleSort(field)}
      className="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider cursor-pointer hover:bg-white/10 transition-colors"
    >
      <div className="flex items-center gap-1">
        {children}
        {sortField === field && (
          <span className="text-cyan-400">
            {sortDirection === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </th>
  );

  return (
    <div className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl">
      <div className="px-6 py-4 border-b border-white/10 bg-gradient-to-r from-cyan-900/10 to-fuchsia-900/10">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-white">
            Participants ({filteredParticipants.length})
          </h3>
          {selectedParticipants.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-cyan-100/80">
                {selectedParticipants.length} selected
              </span>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-3 py-1 bg-gradient-to-r from-red-600 to-red-700 text-white text-sm rounded-lg hover:from-red-500 hover:to-red-600 transition-all shadow-lg shadow-red-500/20"
              >
                Delete Selected
              </motion.button>
            </div>
          )}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-white/10">
          <thead className="bg-gradient-to-r from-white/5 to-white/10">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedParticipants.length === filteredParticipants.length && filteredParticipants.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-white/20 bg-white/5 text-cyan-600 focus:ring-cyan-500 focus:ring-offset-0"
                />
              </th>
              <SortableHeader field="name">Name</SortableHeader>
              <SortableHeader field="email">Email</SortableHeader>
              <SortableHeader field="university">University</SortableHeader>
              <SortableHeader field="team_name">Team</SortableHeader>
              <SortableHeader field="created_at">Registered</SortableHeader>
              <th className="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-gradient-to-br from-white/5 to-white/10 divide-y divide-white/10">
            {filteredParticipants.map((participant, index) => (
              <motion.tr
                key={participant.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                className="hover:bg-white/5 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedParticipants.includes(participant.id)}
                    onChange={(e) => handleSelectParticipant(participant.id, e.target.checked)}
                    className="rounded border-white/20 bg-white/5 text-cyan-600 focus:ring-cyan-500 focus:ring-offset-0"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-cyan-600 to-fuchsia-600 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-lg shadow-cyan-500/20">
                      {participant.first_name[0]}{participant.last_name[0]}
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-white">
                        {participant.first_name} {participant.last_name}
                      </div>
                      <div className="text-sm text-white/60">
                        {participant.student_id}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-white/40" />
                    <span className="text-sm text-white/80">{participant.email}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white/80">
                  {participant.university}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {participant.team_name ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-emerald-600/20 to-emerald-500/20 border border-emerald-400/30 text-emerald-300">
                      {participant.team_name}
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-gray-600/20 to-gray-500/20 border border-gray-400/30 text-gray-300">
                      No Team
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white/60">
                  {new Date(participant.created_at).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center gap-2">
                    <motion.button
                      onClick={() => onViewParticipant(participant)}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-cyan-400 hover:text-cyan-300 transition-colors p-1 rounded-lg hover:bg-white/10"
                      title="View Details"
                    >
                      <Eye className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      onClick={() => onEditParticipant(participant)}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-blue-400 hover:text-blue-300 transition-colors p-1 rounded-lg hover:bg-white/10"
                      title="Edit"
                    >
                      <Edit className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      onClick={() => handleDeleteParticipant(participant.id)}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-red-400 hover:text-red-300 transition-colors p-1 rounded-lg hover:bg-white/10"
                      title="Delete"
                    >
                      <Trash2 className="w-4 h-4" />
                    </motion.button>
                  </div>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredParticipants.length === 0 && (
        <div className="text-center py-12">
          <p className="text-white/60">No participants found matching your criteria.</p>
        </div>
      )}
    </div>
  );
}
