'use client';

import { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Users, UserPlus, UserMinus } from 'lucide-react';
import { useDashboard } from '@/contexts/DashboardContext';
import { createClient } from '@supabase/supabase-js';
import { motion } from 'framer-motion';

// Create admin client for team operations that bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhkbnhneXBjZnZoa2xqbHphZ3ZjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDMyNjQ5NSwiZXhwIjoyMDY1OTAyNDk1fQ.wWBwuigDLWQegzcyGcxMs51sVcWIyiTl8HYnQUd7pwY'
);

export default function TeamManagement() {
  const { participants, teams, loadTeams, createTeam } = useDashboard();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newTeam, setNewTeam] = useState({
    name: '',
    description: '',
    max_members: 4
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Load teams on component mount
  useEffect(() => {
    loadTeams();
  }, []);

  const handleCreateTeam = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await createTeam(newTeam);
      setNewTeam({ name: '', description: '', max_members: 4 });
      setShowCreateForm(false);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTeam = async (teamId) => {
    if (!window.confirm('Are you sure you want to delete this team? This will remove all team members.')) {
      return;
    }

    try {
      const { error } = await supabaseAdmin
        .from('teams')
        .delete()
        .eq('id', teamId);

      if (error) throw error;

      // Reload teams
      loadTeams();
    } catch (error) {
      console.error('Error deleting team:', error);
      alert('Failed to delete team: ' + error.message);
    }
  };

  const addParticipantToTeam = async (teamId, participantId) => {
    try {
      const { error } = await supabaseAdmin
        .from('team_members')
        .insert([{
          team_id: teamId,
          participant_id: participantId
        }]);

      if (error) throw error;

      // Reload teams and participants
      loadTeams();
    } catch (error) {
      console.error('Error adding participant to team:', error);
      alert('Failed to add participant to team: ' + error.message);
    }
  };

  const removeParticipantFromTeam = async (teamId, participantId) => {
    try {
      const { error } = await supabaseAdmin
        .from('team_members')
        .delete()
        .eq('team_id', teamId)
        .eq('participant_id', participantId);

      if (error) throw error;

      // Reload teams
      loadTeams();
    } catch (error) {
      console.error('Error removing participant from team:', error);
      alert('Failed to remove participant from team: ' + error.message);
    }
  };

  // Get participants not in any team
  const unassignedParticipants = participants.filter(participant => 
    !teams.some(team => 
      team.team_members?.some(member => member.participant_id === participant.id)
    )
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Team Management</h2>
        <motion.button
          onClick={() => setShowCreateForm(true)}
          whileHover={{ y: -2, scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-cyan-600 to-fuchsia-600 text-white rounded-xl hover:from-cyan-500 hover:to-fuchsia-500 transition-all shadow-lg shadow-cyan-500/20"
        >
          <Plus className="w-4 h-4" />
          Create Team
        </motion.button>
      </div>

      {/* Create Team Form */}
      {showCreateForm && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-6"
        >
          <h3 className="text-lg font-medium text-white mb-4">Create New Team</h3>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4 p-4 rounded-xl bg-red-600/20 border border-red-400/50 text-white"
            >
              {error}
            </motion.div>
          )}

          <form onSubmit={handleCreateTeam} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-white/70 mb-2">
                  Team Name *
                </label>
                <input
                  type="text"
                  value={newTeam.name}
                  onChange={(e) => setNewTeam({ ...newTeam, name: e.target.value })}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white/70 mb-2">
                  Max Members
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={newTeam.max_members}
                  onChange={(e) => setNewTeam({ ...newTeam, max_members: parseInt(e.target.value) })}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white/70 mb-2">
                Description
              </label>
              <textarea
                value={newTeam.description}
                onChange={(e) => setNewTeam({ ...newTeam, description: e.target.value })}
                rows="3"
                className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
              />
            </div>

            <div className="flex justify-end gap-3">
              <motion.button
                type="button"
                onClick={() => setShowCreateForm(false)}
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white font-medium rounded-xl text-sm transition-colors"
              >
                Cancel
              </motion.button>
              <motion.button
                type="submit"
                disabled={loading}
                whileHover={{ y: -2, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="px-6 py-2.5 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-500 hover:to-teal-500 text-white font-semibold rounded-xl text-sm shadow-lg shadow-emerald-500/20 disabled:opacity-50 transition-all"
              >
                {loading ? 'Creating...' : 'Create Team'}
              </motion.button>
            </div>
          </form>
        </motion.div>
      )}

      {/* Teams Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {teams.map((team, index) => (
          <motion.div
            key={team.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ y: -2, scale: 1.02 }}
            className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-6 hover:shadow-cyan-500/20 transition-all duration-300"
          >
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-medium text-white">{team.name}</h3>
                {team.description && (
                  <p className="text-sm text-white/70 mt-1">{team.description}</p>
                )}
                <div className="flex items-center gap-4 mt-2 text-sm text-white/60">
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {team.current_members || 0}/{team.max_members} members
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <motion.button
                  onClick={() => handleDeleteTeam(team.id)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-2 text-red-400 hover:bg-red-600/20 rounded-lg transition-colors"
                  title="Delete Team"
                >
                  <Trash2 className="w-4 h-4" />
                </motion.button>
              </div>
            </div>

            {/* Team Members */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white/70">Team Members</h4>
              {team.team_members && team.team_members.length > 0 ? (
                <div className="space-y-2">
                  {team.team_members.map((member) => (
                    <div key={member.participant_id} className="flex items-center justify-between p-2 bg-white/5 rounded-lg border border-white/10">
                      <span className="text-sm text-white/80">
                        {member.participants?.first_name} {member.participants?.last_name}
                      </span>
                      <motion.button
                        onClick={() => removeParticipantFromTeam(team.id, member.participant_id)}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="p-1 text-red-400 hover:bg-red-600/20 rounded transition-colors"
                        title="Remove from team"
                      >
                        <UserMinus className="w-3 h-3" />
                      </motion.button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-white/50 italic">No members yet</p>
              )}
            </div>

            {/* Add Member Dropdown */}
            {unassignedParticipants.length > 0 && team.current_members < team.max_members && (
              <div className="mt-4">
                <select
                  onChange={(e) => {
                    if (e.target.value) {
                      addParticipantToTeam(team.id, e.target.value);
                      e.target.value = '';
                    }
                  }}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-3 py-2.5 text-white text-sm focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                >
                  <option value="" className="bg-gray-800 text-white">Add participant to team...</option>
                  {unassignedParticipants.map((participant) => (
                    <option key={participant.id} value={participant.id} className="bg-gray-800 text-white">
                      {participant.first_name} {participant.last_name} ({participant.university})
                    </option>
                  ))}
                </select>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Unassigned Participants */}
      {unassignedParticipants.length > 0 && (
        <div className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-6">
          <h3 className="text-lg font-medium text-white mb-4">
            Unassigned Participants ({unassignedParticipants.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {unassignedParticipants.map((participant) => (
              <motion.div
                key={participant.id}
                whileHover={{ scale: 1.02 }}
                className="p-3 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors"
              >
                <div className="text-sm font-medium text-white">
                  {participant.first_name} {participant.last_name}
                </div>
                <div className="text-xs text-white/60">{participant.university}</div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
