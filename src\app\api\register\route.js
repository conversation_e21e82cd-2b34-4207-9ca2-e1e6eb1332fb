import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Use service role key for server-side operations that bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export async function POST(request) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = [
      'firstName', 'lastName', 'email', 'phone', 
      'university', 'studentId', 'skills', 'expectations'
    ]
    
    const missingFields = requiredFields.filter(field => !body[field]?.trim())
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      )
    }

    // Check if email already exists
    const { data: existingParticipant } = await supabaseAdmin
      .from('participants')
      .select('email')
      .eq('email', body.email.toLowerCase().trim())
      .single()

    if (existingParticipant) {
      return NextResponse.json(
        { error: 'A participant with this email is already registered' },
        { status: 409 }
      )
    }

    // Prepare participant data
    const participantData = {
      first_name: body.firstName.trim(),
      last_name: body.lastName.trim(),
      email: body.email.toLowerCase().trim(),
      phone: body.phone.trim(),
      discord_tag: body.discordTag?.trim() || null,
      university: body.university.trim(),
      student_id: body.studentId.trim(),
      skills: body.skills.trim(),
      expectations: body.expectations.trim(),
      has_team: body.hasTeam === 'yes',
      team_name: body.hasTeam === 'yes' ? body.teamName?.trim() : null,
      team_member_1: body.hasTeam === 'yes' ? body.teamMember1?.trim() : null,
      team_member_2: body.hasTeam === 'yes' ? body.teamMember2?.trim() : null,
      team_member_3: body.hasTeam === 'yes' ? body.teamMember3?.trim() : null
    }

    // Insert participant into database
    const { data: participant, error: insertError } = await supabaseAdmin
      .from('participants')
      .insert([participantData])
      .select()
      .single()

    if (insertError) {
      console.error('Database insert error:', insertError)

      // Handle specific error cases
      if (insertError.code === '23505') {
        return NextResponse.json(
          { error: 'A participant with this email is already registered.' },
          { status: 409 }
        )
      }

      return NextResponse.json(
        { error: `Registration failed: ${insertError.message}` },
        { status: 500 }
      )
    }

    // If participant has a team, try to create/update team record
    if (participantData.has_team && participantData.team_name) {
      try {
        // Check if team already exists
        const { data: existingTeam } = await supabaseAdmin
          .from('teams')
          .select('id, current_members, max_members')
          .eq('name', participantData.team_name)
          .single()

        let teamId

        if (existingTeam) {
          // Team exists, check if it has space
          if (existingTeam.current_members >= existingTeam.max_members) {
            console.warn(`Team ${participantData.team_name} is full`)
            // Still register the participant but don't add to team
          } else {
            teamId = existingTeam.id
          }
        } else {
          // Create new team
          const { data: newTeam, error: teamError } = await supabaseAdmin
            .from('teams')
            .insert([{
              name: participantData.team_name,
              description: `Team created by ${participantData.first_name} ${participantData.last_name}`,
              max_members: 4,
              current_members: 0
            }])
            .select()
            .single()

          if (teamError) {
            console.error('Team creation error:', teamError)
          } else {
            teamId = newTeam.id
          }
        }

        // Add participant to team if team was created/found and has space
        if (teamId) {
          const { error: memberError } = await supabaseAdmin
            .from('team_members')
            .insert([{
              team_id: teamId,
              participant_id: participant.id,
              role: existingTeam ? 'member' : 'leader'
            }])

          if (memberError) {
            console.error('Team member addition error:', memberError)
          }
        }
      } catch (teamError) {
        console.error('Team processing error:', teamError)
        // Don't fail registration if team processing fails
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Registration successful!',
      participant: {
        id: participant.id,
        name: `${participant.first_name} ${participant.last_name}`,
        email: participant.email
      }
    })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}
