'use client';

import { useState, useEffect, useRef } from 'react';
import { Trash2, UserPlus, Download, Mail, X, ChevronDown, FileText, FilePlus } from 'lucide-react';
import { useDashboard } from '@/contexts/DashboardContext';
import { createClient } from '@supabase/supabase-js';
import { exportSelectedParticipants, appendSelectedParticipantsToFile } from '@/lib/csvExport';
import { motion } from 'framer-motion';
import { createPortal } from 'react-dom';

// Create admin client for team operations that bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhkbnhneXBjZnZoa2xqbHphZ3ZjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDMyNjQ5NSwiZXhwIjoyMDY1OTAyNDk1fQ.wWBwuigDLWQegzcyGcxMs51sVcWIyiTl8HYnQUd7pwY'
);

export default function BulkOperations({ selectedParticipants, onClearSelection }) {
  const { participants, teams, deleteParticipant, loadParticipants, loadTeams } = useDashboard();
  const [showBulkMenu, setShowBulkMenu] = useState(false);
  const [showAssignTeamModal, setShowAssignTeamModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState('');
  const [loading, setLoading] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const exportMenuRef = useRef(null);

  const selectedCount = selectedParticipants.length;
  const selectedData = participants.filter(p => selectedParticipants.includes(p.id));

  const handleExportMenuToggle = () => {
    if (!showExportMenu && exportMenuRef.current) {
      const rect = exportMenuRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.top - 8, // Position above the button
        left: rect.left
      });
    }
    setShowExportMenu(!showExportMenu);
  };

  // Close export menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (exportMenuRef.current && !exportMenuRef.current.contains(event.target)) {
        setShowExportMenu(false);
      }
    };

    const handleResize = () => {
      if (showExportMenu && exportMenuRef.current) {
        const rect = exportMenuRef.current.getBoundingClientRect();
        setDropdownPosition({
          top: rect.top - 8,
          left: rect.left
        });
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('resize', handleResize);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
    };
  }, [showExportMenu]);

  const handleBulkDelete = async () => {
    setLoading(true);
    try {
      // Delete all selected participants
      const deletePromises = selectedParticipants.map(id => deleteParticipant(id));
      await Promise.all(deletePromises);

      onClearSelection();
      setShowDeleteConfirm(false);
      alert(`Successfully deleted ${selectedParticipants.length} participant${selectedParticipants.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('Error deleting participants:', error);
      alert('Failed to delete some participants: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkAssignToTeam = async () => {
    if (!selectedTeam) {
      alert('Please select a team');
      return;
    }

    setLoading(true);
    try {
      const team = teams.find(t => t.id === selectedTeam);
      if (!team) {
        throw new Error('Team not found');
      }

      // Check if team has enough space
      const currentMembers = team.current_members || 0;
      const availableSpots = team.max_members - currentMembers;

      if (selectedCount > availableSpots) {
        alert(`Team "${team.name}" only has ${availableSpots} available spots, but you selected ${selectedCount} participants.`);
        return;
      }

      // Remove participants from their current teams first
      const removePromises = selectedParticipants.map(async (participantId) => {
        const { error } = await supabaseAdmin
          .from('team_members')
          .delete()
          .eq('participant_id', participantId);

        if (error) throw error;
      });

      await Promise.all(removePromises);

      // Add participants to the new team
      const addPromises = selectedParticipants.map(async (participantId) => {
        const { error } = await supabaseAdmin
          .from('team_members')
          .insert([{
            team_id: selectedTeam,
            participant_id: participantId,
            role: 'member'
          }]);

        if (error) throw error;
      });

      await Promise.all(addPromises);

      // Reload data
      await loadParticipants();
      await loadTeams();

      onClearSelection();
      setShowAssignTeamModal(false);
      setSelectedTeam('');
      alert(`Successfully assigned ${selectedParticipants.length} participant${selectedParticipants.length > 1 ? 's' : ''} to team "${team.name}"`);
    } catch (error) {
      console.error('Error assigning participants to team:', error);
      alert('Failed to assign participants to team: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkRemoveFromTeams = async () => {
    setLoading(true);
    try {
      const removePromises = selectedParticipants.map(async (participantId) => {
        const { error } = await supabaseAdmin
          .from('team_members')
          .delete()
          .eq('participant_id', participantId);

        if (error) throw error;
      });

      await Promise.all(removePromises);

      // Reload data
      await loadParticipants();
      await loadTeams();

      onClearSelection();
      alert(`Successfully removed ${selectedParticipants.length} participant${selectedParticipants.length > 1 ? 's' : ''} from their teams`);
    } catch (error) {
      console.error('Error removing participants from teams:', error);
      alert('Failed to remove participants from teams: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkExport = () => {
    exportSelectedParticipants(participants, selectedParticipants, `selected_${selectedCount}_participants.csv`);
    setShowExportMenu(false);
  };

  const handleAppendToCSV = () => {
    appendSelectedParticipantsToFile(
      participants,
      selectedParticipants,
      (count, filename) => {
        alert(`✅ Success!\n\nAppended ${count} participant${count > 1 ? 's' : ''} to your CSV file.\n\nDownloaded as: ${filename}\n\nThe new file contains your original data plus the selected participants.`);
      }
    );
    setShowExportMenu(false);
  };

  const handleSendBulkEmail = () => {
    const emails = selectedData.map(p => p.email).join(',');
    const subject = encodeURIComponent('NCS Hack 2025 - Important Update');
    const body = encodeURIComponent('Dear participants,\n\nWe have an important update regarding NCS Hack 2025.\n\nBest regards,\nNCS Hack Team');
    
    window.open(`mailto:${emails}?subject=${subject}&body=${body}`);
  };

  if (selectedCount === 0) return null;

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl p-4 z-40 max-w-[95vw]"
      >
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-white">
              {selectedCount} participant{selectedCount > 1 ? 's' : ''} selected
            </span>
            <motion.button
              onClick={onClearSelection}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="p-1 hover:bg-white/10 rounded transition-colors"
              title="Clear selection"
            >
              <X className="w-4 h-4 text-white/60" />
            </motion.button>
          </div>

          <div className="flex flex-wrap items-center gap-2">
            <div className="relative">
              <motion.button
                ref={exportMenuRef}
                onClick={handleExportMenuToggle}
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-xl hover:from-emerald-500 hover:to-teal-500 transition-all text-sm shadow-lg shadow-emerald-500/20"
                title="Export options"
              >
                <Download className="w-4 h-4" />
                Export
                <ChevronDown className="w-3 h-3" />
              </motion.button>
            </div>

            <motion.button
              onClick={handleSendBulkEmail}
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-500 hover:to-blue-600 transition-all text-sm shadow-lg shadow-blue-500/20"
              title="Send email to selected participants"
            >
              <Mail className="w-4 h-4" />
              Email
            </motion.button>

            <motion.button
              onClick={() => setShowAssignTeamModal(true)}
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-cyan-600 to-fuchsia-600 text-white rounded-xl hover:from-cyan-500 hover:to-fuchsia-500 transition-all text-sm shadow-lg shadow-cyan-500/20"
              title="Assign to team"
            >
              <UserPlus className="w-4 h-4" />
              Assign Team
            </motion.button>

            <motion.button
              onClick={handleBulkRemoveFromTeams}
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-orange-600 to-orange-700 text-white rounded-xl hover:from-orange-500 hover:to-orange-600 transition-all text-sm shadow-lg shadow-orange-500/20"
              title="Remove from teams"
              disabled={loading}
            >
              <UserPlus className="w-4 h-4 rotate-180" />
              Remove from Teams
            </motion.button>

            <motion.button
              onClick={() => setShowDeleteConfirm(true)}
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:from-red-500 hover:to-red-600 transition-all text-sm shadow-lg shadow-red-500/20"
              title="Delete selected participants"
            >
              <Trash2 className="w-4 h-4" />
              Delete
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Portal for export dropdown */}
      {showExportMenu && typeof window !== 'undefined' && createPortal(
        <>
          <div
            className="fixed inset-0 z-[80]"
            onClick={() => setShowExportMenu(false)}
          />
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed w-48 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl backdrop-blur-xl rounded-xl z-[85]"
            style={{
              top: dropdownPosition.top - 160, // Position above the floating bar
              left: dropdownPosition.left,
              transform: 'translateY(-100%)'
            }}
          >
            <div className="py-1">
              <motion.button
                onClick={handleBulkExport}
                whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                className="w-full text-left px-4 py-2 text-sm text-white/80 hover:text-white transition-colors flex items-center gap-2"
                title="Download selected participants as a new CSV file"
              >
                <FileText className="w-4 h-4" />
                <div>
                  <div>Export New CSV</div>
                  <div className="text-xs text-white/50">Create new file</div>
                </div>
              </motion.button>
              <motion.button
                onClick={handleAppendToCSV}
                whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                className="w-full text-left px-4 py-2 text-sm text-white/80 hover:text-white transition-colors flex items-center gap-2"
                title="Add selected participants to an existing CSV file from your computer"
              >
                <FilePlus className="w-4 h-4" />
                <div>
                  <div>Append to Existing CSV</div>
                  <div className="text-xs text-white/50">Add to existing file</div>
                </div>
              </motion.button>
            </div>
          </motion.div>
        </>,
        document.body
      )}

      {/* Assign Team Modal */}
      {showAssignTeamModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-[70] backdrop-blur-sm">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl max-w-md w-full p-6"
          >
            <h3 className="text-lg font-medium text-white mb-4">
              Assign {selectedCount} participant{selectedCount > 1 ? 's' : ''} to team
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white/70 mb-2">
                  Select Team
                </label>
                <select
                  value={selectedTeam}
                  onChange={(e) => setSelectedTeam(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-3 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-200"
                >
                  <option value="" className="bg-gray-800 text-white">Choose a team...</option>
                  {teams.map((team) => (
                    <option key={team.id} value={team.id} className="bg-gray-800 text-white">
                      {team.name} ({team.current_members || 0}/{team.max_members})
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex justify-end gap-3">
                <motion.button
                  onClick={() => setShowAssignTeamModal(false)}
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white font-medium rounded-xl text-sm transition-colors"
                >
                  Cancel
                </motion.button>
                <motion.button
                  onClick={handleBulkAssignToTeam}
                  disabled={loading || !selectedTeam}
                  whileHover={{ y: -2, scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-2.5 bg-gradient-to-r from-cyan-600 to-fuchsia-600 hover:from-cyan-500 hover:to-fuchsia-500 text-white font-semibold rounded-xl text-sm shadow-lg shadow-cyan-500/20 disabled:opacity-50 transition-all"
                >
                  {loading ? 'Assigning...' : 'Assign to Team'}
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-[70] backdrop-blur-sm">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gradient-to-br from-white/5 to-white/10 border border-white/10 shadow-2xl overflow-hidden backdrop-blur-xl rounded-xl max-w-md w-full p-6"
          >
            <h3 className="text-lg font-medium text-white mb-4">
              Delete {selectedCount} participant{selectedCount > 1 ? 's' : ''}?
            </h3>

            <p className="text-sm text-white/70 mb-6">
              This action cannot be undone. All selected participants and their team memberships will be permanently deleted.
            </p>

            <div className="flex justify-end gap-3">
              <motion.button
                onClick={() => setShowDeleteConfirm(false)}
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white font-medium rounded-xl text-sm transition-colors"
              >
                Cancel
              </motion.button>
              <motion.button
                onClick={handleBulkDelete}
                disabled={loading}
                whileHover={{ y: -2, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="px-6 py-2.5 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white font-semibold rounded-xl text-sm shadow-lg shadow-red-500/20 disabled:opacity-50 transition-all"
              >
                {loading ? 'Deleting...' : 'Delete All'}
              </motion.button>
            </div>
          </motion.div>
        </div>
      )}
    </>
  );
}
