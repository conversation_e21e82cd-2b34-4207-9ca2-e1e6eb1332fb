"use client"

import { useState } from "react"
import { Calendar, Clock, Users } from "lucide-react"
import ScheduleBackground from "./ScheduleBackground"

export default function ScheduleNcs() {
  // State for active day selection
  const [activeDay, setActiveDay] = useState(0)

  // REALISTIC SCHEDULE DATA 
  const scheduleData = [
    {
      day: "Day 1",
      date: "June 29, 2025",
      theme: "Kickoff & Get Started",
      events: [
        {
          time: "9:00 AM",
          title: "Check-in",
          description: "Check in and make it official—you’ve arrived!",
          type: "registration",
        },
        {
          time: "10:00 AM",
          title: "Opening Ceremony",
          description: "Opening Ceremony starts now! Bring your ambition...Let the hype begin!",
          type: "keynote",
        },
        {
          time: "11:30 AM",
          title: "Lunch time",
          description: "Day one, break one! Get ready for what's next.",
          type: "workshop",
        },
        {
          time: "13:00 PM",
          title: "Challenges reveal, NCSHACK25 is here!",
          description: "Get ready to tackle the challenges head-on!",
          type: "keynote",
        },
        {
          time: "4:45 PM",
          title: "Coffee Break",
          description:"Take a small break and recharge.",
          type: "workshop",
        },
        {
          time: "8:00 PM",
          title: "Dinner Time",
          description: "Enjoy a delicious dinner with fellow participants.",
          type: "networking",
        },
        {
          time: "3:00 AM",
          title: "Snacks Snacks Snacks",
          description: "Night shift crew, we see you. The grind never sleeps (but maybe you should).",
          type: "break",
        },
        
      ],
    },
    {
      day: "Day 2",
      date: "June 30, 2025",
      theme: "Deep Work Mode",
      events: [
        {
          time: "8:30 AM",
          title: "Breakfast",
          description: "Rise and shine! Fuel up for a day of innovation.",
          type: "break",
        },
        {
          time: "9:15 AM",
          title: "Get back to work",
          description: "Time to dive deep into your projects !",
          type: "workshop",
        },
        {
          time: "11:30 PM",
          title: "Lunch Break :)",
          type: "break",
        },
        {
          time: "1:30 PM",
          title: "CODE CODE CODE",
          type: "workshop",
        },
        {
          time: "4:45 PM",
          title: "Coffee Break",
          description: "Take a break, grab a coffee, and recharge.",
          type: "networking",
        },
        {
          time: "8:00 PM",
          title: "Dinner Time",
          description: "Enjoy a meal with your team.",
          type: "break",
        },
        {
          time: "3:00 AM",
          title: "Snacks snacks snacks !",
          description: "Grab a bite, keep the brain happy.",
          type: "networking",
        },
      ],
    },
    {
      day: "Day 3",
      date: "july 01, 2025",
      theme: "Final Push",
      events: [
        {
          time: "8:30 AM",
          title: "Morning Breakfast ",
          description: "Let the coffee do the coding. Day three, let’s gooo!",
          type: "workshop",
        },
        {
          time: "11:00 AM",
          title: "Keep going!",
          description: "You’re almost there!",
          type: "workshop",
        },
        {
          time: "1:00 PM",
          title: "Lunch time :)",
          description: "One last lunch before we wrap this up.",
          type: "break",
        },
        {
          time: "4:00 PM",
          title: "Final hours. Make them count.",
          type: "networking",
        },
        {
          time: "8:00 PM",
          title: "Dinner Time !",
          type: "break",
        },
        {
          time: "10:00 PM",
          title: "Late Night Coding ",
          description: "Push through the night, the deadline is near!",
          type: "workshop",
        },
        {
          time: "3:00 AM",
          title: "Sssssssnacks !",
          type: "break",
        },
      ],
    },
    {
      day: "Day 4",
      date: "July 02, 2025",
      theme: "Submit & Celebrate",
      events: [
        {
          time: "8:30 AM",
          title: "Last Breakfast",
          description: "Final breakfast, the big day is here.",
          type: "workshop",
        },
        {
          time: "10:30 PM",
          title: "Project Finalization & Submission",
          description: "",
          type: "closing",
        },
        {
          time: "11:00 PM",
          title: "Lunch time !",
          type: "break",
        },
        {
          time: "12:30 PM",
          title: "Pitch & Presentation ",
          description: "Time to pitch! Show us what you built—and why it matters.",
          type: "workshop",
        },
        {
          time: "3:00 PM",
          title: "Small Break",
          description: "Take a break before the big reveal.",
          type: "keynote",
        },
        {
          time: "3:30 PM",
          title: "Deliberations and Annoucing Winners.",
          type: "workshop",
        },
        {
          time: "4:00 PM",
          title: "Closing Ceremony & Networking.",

          type: "networking",
        },
      ],
    },
  ]

  const getEventTypeStyle = (type) => {
    const styles = {
      // REGISTRATION - Light blue 
      registration: "bg-blue-200 border-blue-200 shadow-lg transform rotate-1 hover:rotate-0",

      // KEYNOTE - Pink/violet 
      keynote: "bg-violet-200 border-pink-200 shadow-lg transform -rotate-1 hover:rotate-0",

      // WORKSHOP - White 
      workshop: "bg-white border-gray-200 shadow-lg transform rotate-2 hover:rotate-0",

      // BREAK - Light pink 
      break: "bg-pink-50 border-pink-100 shadow-lg transform -rotate-2 hover:rotate-0",

      // NETWORKING - Light blue 
      networking: "bg-blue-100 border-blue-200 shadow-lg transform rotate-1 hover:rotate-0",

      // CLOSING - Pink/violet 
      closing: "bg-pink-100 border-pink-200 shadow-lg transform -rotate-1 hover:rotate-0",
    }
    return styles[type] || styles.workshop
  }

  // EVENT ICONS
  const getEventIcon = (type) => {
    const icons = {
      // REGISTRATION ICON -
      registration: <Users className="w-4 h-4 text-gray-800" />,

      // KEYNOTE ICON - 
      keynote: <Calendar className="w-4 h-4 text-gray-800" />,

      // WORKSHOP ICON -
      workshop: <Clock className="w-4 h-4 text-gray-800" />,

      // BREAK ICON - 
      break: <Coffee className="w-4 h-4 text-gray-800" />,

      // NETWORKING ICON - 
      networking: <Users className="w-4 h-4 text-gray-800" />,

      // CLOSING ICON - 
      closing: <Calendar className="w-4 h-4 text-gray-800" />,
    }
    return icons[type] || <Clock className="w-4 h-4 text-gray-800" />
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="relative bg-black overflow-hidden">
        <ScheduleBackground />
        <div className="relative z-10 px-3 sm:px-4 md:px-8 py-8 sm:py-12">
          {/* HEADER SECTION */}
          <div className="text-center mb-12 animate-fade-in">
            {/* MAIN TITLE */}
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-[#3BC2EC] via-[#C05AA2] to-white bg-clip-text text-transparent mb-4">
              Event Schedule
            </h1>
            {/* SUBTITLE*/}
            <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-4">
          A journey of 4 days packed with ideas, collaboration, and breakthroughs.
            </p>
          </div>

          {/* DAY SELECTOR TABS*/}
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-8 px-2">
            {scheduleData.map((dayData, index) => (
              <button
                key={index}
                onClick={() => setActiveDay(index)}
                className={`px-3 sm:px-6 py-2 sm:py-3 rounded-full transition-all duration-300 hover:scale-105 active:scale-95 text-sm sm:text-base font-bold ${
                  activeDay === index
                    ? // ACTIVE TAB STYLING 
                      "bg-white text-black shadow-xl border-2 border-white"
                    : // INACTIVE TAB STYLING 
                      "bg-white/80 text-black hover:bg-white border border-white/50"
                }`}
              >
                <div className="text-center">
                  {/* DAY NAME */}
                  <div className="font-bold">{dayData.day}</div>
                  {/* DATE */}
                  <div className="text-xs sm:text-sm font-bold opacity-70">{dayData.date}</div>
                </div>
              </button>
            ))}
          </div>

          {/* SCHEDULE CONTENT */}
          <div key={activeDay} className="max-w-4xl mx-auto animate-slide-in-fast">
            {/* Day Theme Header */}
            <div className="text-center mb-6 sm:mb-8">
              {/* DAY TITLE  */}
              <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2">{scheduleData[activeDay].day}</h2>
              {/* DAY THEME*/}
              <p className="text-base sm:text-lg text-black font-bold bg-white rounded-full px-4 sm:px-6 py-2 inline-block border-2 border-white">
                {scheduleData[activeDay].theme}
              </p>
            </div>

            {/* Events Timeline */}
            <div className="space-y-3 sm:space-y-4">
              {scheduleData[activeDay].events.map((event, eventIndex) => (
                <div
                  key={eventIndex}
                  
                  className={`rounded-xl sm:rounded-2xl border-2 p-4 sm:p-6 hover:shadow-2xl transition-all duration-200 animate-fade-in-fast ${getEventTypeStyle(event.type)}`}
                  style={{ animationDelay: `${eventIndex * 0.02}s` }}
                >
                  <div className="flex flex-col sm:flex-col md:flex-row md:items-center gap-3 sm:gap-4">
                    {/* Time Badge */}
                    <div className="flex-shrink-0">
                      <div className="bg-gray-100 rounded-full px-3 sm:px-4 py-1.5 sm:py-2 border-2 border-gray-300">
                        <div className="flex items-center gap-2">
                          {/* TIME ICON  */}
                          <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-black" />
                          {/* TIME TEXT  */}
                          <span className="font-bold text-sm sm:text-base text-black">{event.time}</span>
                        </div>
                      </div>
                    </div>

                    {/* Event Details */}
                    <div className="flex-grow">
                      <div className="flex items-start gap-2 sm:gap-3">
                        {/* EVENT TYPE ICON - Colors defined in getEventIcon function above */}
                        <div className="flex-shrink-0 mt-1">{getEventIcon(event.type)}</div>
                        <div>
                          {/* EVENT TITLE */}
                          <h3 className="text-lg sm:text-xl font-bold text-black mb-1">{event.title}</h3>
                          {/* EVENT DESCRIPTION */}
                          <p className="text-sm sm:text-base text-gray-700 font-medium mb-2">{event.description}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            

      
      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slide-in-fast {
          from { opacity: 0; transform: translateX(10px); }
          to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fade-in-fast {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in { animation: fade-in 0.3s ease-out; }
        .animate-slide-in-fast { animation: slide-in-fast 0.2s ease-out; }
        .animate-fade-in-fast { animation: fade-in-fast 0.15s ease-out forwards; opacity: 0; }
      `}</style>
        </div>
      </div>
    </div>
    </div>
  )
}


function Coffee({ className }) {
  return (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 8h1a4 4 0 010 8h-1m-10-8v8a4 4 0 004 4h4a4 4 0 004-4V8M7 8V6a2 2 0 012-2h6a2 2 0 012 2v2"
      />
    </svg>
  )
}
