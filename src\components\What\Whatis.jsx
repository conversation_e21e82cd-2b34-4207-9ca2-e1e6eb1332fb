export default function WhatIsNcshack() {
  return (
    <section className="bg-black text-white py-16 px-6 md:px-20 relative overflow-hidden">
      {/* Title */}
      <h2 className="text-3xl md:text-6xl font-bold text-center mb-10">
        What is <span className="text-sky-400">NCSHACK</span>?
      </h2>

      {/* Top content with arrow */}
      <div className="flex flex-col-reverse md:flex-row items-start justify-between gap-8">
        {/* Left Content */}
        <div className="md:w-2/3 space-y-6">
          <p className="text-xl md:text-xl font-semibold">
            NCSHACK is back for its{" "}
            <span className="text-pink-400">second edition</span>... bigger,
            bolder, and more creative than ever.
          </p>
          <p className="text-lg md:text-xl">
            Across 4 full days, participants will collaborate to turn real ideas
            into working websites, guided by expert mentors and smart use of AI.
          </p>
        </div>

        {/* Arrow with slashes above it — shown only on md+ */}
        <div className="md:w-1/3 w-full hidden md:flex flex-col items-end relative">
          {/* Slashes above arrow */}
          <div className="flex space-x-2 mb-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="w-1 h-6 bg-pink-400 rotate-12"></div>
            ))}
          </div>

          {/* Arrow */}
          <img
            src="/images/CURSOOR.svg"
            alt="Arrow"
            className="w-24 md:w-40  drop-shadow-[0_5px_10px_rgba(173,216,230,0.4)]"
          />
        </div>
      </div>

      {/* Bottom row: Xs on the left, paragraph on the right (stacked on mobile) */}
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6 pt-10">
        {/* X icons */}
        <div className="flex space-x-3">
          <div className="text-3xl md:text-5xl font-bold text-white">×</div>
          <div className="text-3xl md:text-5xl font-bold text-pink-400">×</div>
          <div className="text-3xl md:text-5xl font-bold text-sky-400">×</div>
          <div className="text-3xl md:text-5xl font-bold text-indigo-600">×</div>
        </div>

        {/* Closing Text */}
        <p className="text-base md:text-xl font-semibold max-w-3xl">
          The event concludes with impactful presentations and a vibrant closing
          ceremony, spotlighting the creativity and drive of Algeria’s next tech
          leaders.
        </p>
      </div>
    </section>
  );
}
