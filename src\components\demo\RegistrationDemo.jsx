// RegistrationDemo.jsx - For testing both registration states
"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import RegistrationForm from "../form/RegistrationForm";
import RegistrationClosed from "../form/RegistrationClosed";

export default function RegistrationDemo() {
  const [showOpen, setShowOpen] = useState(false);
  const [showClosed, setShowClosed] = useState(false);

  return (
    <div className="min-h-screen bg-black text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 bg-clip-text text-transparent bg-gradient-to-r from-cyan-300 to-fuchsia-300">
          Registration States Demo
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Open Registration Demo */}
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold text-cyan-300">Open Registration</h2>
            <p className="text-white/70">
              This is how the registration form appears when registration is open.
            </p>
            <motion.button
              onClick={() => setShowOpen(true)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-gradient-to-r from-cyan-600 to-purple-600 rounded-xl text-white font-semibold"
            >
              Show Open Registration
            </motion.button>
          </div>

          {/* Closed Registration Demo */}
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold text-fuchsia-300">Closed Registration</h2>
            <p className="text-white/70">
              This is how the registration appears when registration is closed.
            </p>
            <motion.button
              onClick={() => setShowClosed(true)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-gradient-to-r from-fuchsia-600 to-cyan-600 rounded-xl text-white font-semibold"
            >
              Show Closed Registration
            </motion.button>
          </div>
        </div>

        {/* Configuration Instructions */}
        <div className="mt-12 p-6 bg-gradient-to-r from-white/5 to-white/10 border border-white/10 rounded-xl">
          <h3 className="text-xl font-semibold mb-4 text-white">How to Control Registration State</h3>
          <div className="space-y-3 text-white/80">
            <p>
              <strong className="text-cyan-300">To close registration:</strong> 
              Set <code className="bg-black/30 px-2 py-1 rounded">REGISTRATION_OPEN = false</code> in <code className="bg-black/30 px-2 py-1 rounded">src/config/registration.js</code>
            </p>
            <p>
              <strong className="text-fuchsia-300">To open registration:</strong> 
              Set <code className="bg-black/30 px-2 py-1 rounded">REGISTRATION_OPEN = true</code> in <code className="bg-black/30 px-2 py-1 rounded">src/config/registration.js</code>
            </p>
            <p className="text-sm text-white/60 mt-4">
              This is a frontend-only toggle that doesn't affect any backend APIs or database logic.
            </p>
          </div>
        </div>
      </div>

      {/* Modal Overlays */}
      {showOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
          <div className="relative">
            <button
              onClick={() => setShowOpen(false)}
              className="absolute -top-10 right-0 text-white hover:text-gray-300"
            >
              ✕
            </button>
            <RegistrationForm onClose={() => setShowOpen(false)} />
          </div>
        </div>
      )}

      {showClosed && (
        <RegistrationClosed onClose={() => setShowClosed(false)} />
      )}
    </div>
  );
}
