import { Users, Users2, Calendar } from "lucide-react";
import './Boxes.css';
function Boxes() {
    return (
    <div className="boxes-container">
    <div className="boxes-wrapper">
        <div className="box">
        <div className="box-icon">
            <Users />
        </div>
        <h2>Participants</h2>
        <div className="box-number">80+</div>
        <p>Students from various <br/> universities--creative minds.</p>
        </div>

        <div className="box">
        <div className="box-icon">
            <Users2 />
        </div>
        <h2>Mentors</h2>
        <div className="box-number">15+</div>
        <p>Here to support, inspire and help <br/> you push your projects further.</p>
        </div>

        <div className="box">
        <div className="box-icon">
            <Calendar />
        </div>
        <h2>Days</h2>
        <div className="box-number">4</div>
        <p> Design, develop, bring your vision to life !</p>
        </div>
    </div>
    <div className="boxes-dots">
  <span className="dot1" />
  <span className="dot2" />
  <span className="dot3" />
</div>

    </div>
);
}

export default Boxes;