'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

const HrAuthContext = createContext({});

export const useHrAuth = () => {
  const context = useContext(HrAuthContext);
  if (!context) {
    throw new Error('useHrAuth must be used within an HrAuthProvider');
  }
  return context;
};

export function HrAuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [hrUser, setHrUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        setUser(session.user);
        await checkHrUser(session.user.id);
      }
      
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setUser(session.user);
          await checkHrUser(session.user.id);
        } else {
          setUser(null);
          setHrUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const checkHrUser = async (userId) => {
    try {
      const { data: hrUserData, error } = await supabase
        .from('hr_users')
        .select('*')
        .eq('auth_user_id', userId)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching HR user:', error);
        setHrUser(null);
        return;
      }

      setHrUser(hrUserData);
    } catch (error) {
      console.error('Error checking HR user:', error);
      setHrUser(null);
    }
  };

  const signIn = async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      // Check if user is HR user
      const { data: hrUserData, error: hrError } = await supabase
        .from('hr_users')
        .select('*')
        .eq('auth_user_id', data.user.id)
        .eq('is_active', true)
        .single();

      if (hrError || !hrUserData) {
        await supabase.auth.signOut();
        throw new Error('Access denied. You are not authorized to access the HR dashboard.');
      }

      return { user: data.user, hrUser: hrUserData };
    } catch (error) {
      throw error;
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      setUser(null);
      setHrUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const value = {
    user,
    hrUser,
    loading,
    signIn,
    signOut,
    isAuthenticated: !!user && !!hrUser
  };

  return (
    <HrAuthContext.Provider value={value}>
      {children}
    </HrAuthContext.Provider>
  );
}
